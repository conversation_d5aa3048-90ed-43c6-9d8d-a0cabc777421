import { injectable, inject } from "inversify";
import Store from "electron-store";
import * as fs from "fs";
import { IPersistenceService, ILogger, TYPES, IEventBus, CoreEvents } from "../../shared/types";

@injectable()
export class PersistenceService implements IPersistenceService {
  private store: Store;

  constructor(
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.EventBus) private eventBus: IEventBus
  ) {
    this.store = new Store({ name: "dev-workbench-ng" });
    this.migrateFromOldConfig();
  }

  private migrateFromOldConfig() {
    const oldConfigPath = "config/app-config.json";
    const migrationFlag = "migration_from_app_config_done";

    if (this.store.get(migrationFlag)) {
      this.logger.info("Old configuration migration has already been performed. Skipping.");
      return;
    }

    try {
      if (fs.existsSync(oldConfigPath)) {
        this.logger.info(`Found old config file at ${oldConfigPath}. Starting migration.`);
        const oldConfigData = fs.readFileSync(oldConfigPath, "utf-8");
        const oldConfig = JSON.parse(oldConfigData);

        if (oldConfig.scanConfigs && Array.isArray(oldConfig.scanConfigs)) {
          this.store.set("scanConfigs", oldConfig.scanConfigs);
          this.logger.info(`Migrated ${oldConfig.scanConfigs.length} scan configurations.`);
        }

        this.store.set(migrationFlag, true);
        this.logger.info("Migration successful. Old config will not be read again.");
      }
    } catch (error) {
      this.logger.error("Failed to migrate from old config file.", error as Error);
    }
  }

  async get<T>(key: string): Promise<T | undefined> {
    return this.store.get(key) as T | undefined;
  }

  async set<T>(key: string, value: T): Promise<void> {
    this.store.set(key, value);
    this.eventBus.emitEvent(CoreEvents.CONFIG_CHANGED, { key, value });
  }

  async delete(key: string): Promise<void> {
    this.store.delete(key);
    this.eventBus.emitEvent(CoreEvents.CONFIG_CHANGED, { key, value: undefined });
  }
}
