import { injectable } from "inversify";
import * as winston from "winston";
import { ILogger } from "../../shared/types";

@injectable()
export class Logger implements ILogger {
  private logger: winston.Logger;

  constructor() {
    this.logger = winston.createLogger({
      level: "info",
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          ),
        }),
        new winston.transports.File({ filename: "devworkbench.log" }),
      ],
    });
  }

  public debug(message: string, meta?: any): void { this.logger.debug(message, meta); }
  public info(message: string, meta?: any): void { this.logger.info(message, meta); }
  public warn(message: string, meta?: any): void { this.logger.warn(message, meta); }
  public error(message: string, error?: Error, meta?: any): void {
    this.logger.error(message, { error: error?.stack, ...meta });
  }
}
