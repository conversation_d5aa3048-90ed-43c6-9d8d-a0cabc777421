import { injectable } from 'inversify';
import * as fs from 'fs';
import * as path from 'path';

export interface ScanConfig {
  id: string;
  name: string;
  paths: string[];
  recursive: boolean;
  excludePatterns: string[];
  includePatterns: string[];
  enabled: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AppConfig {
  scanConfigs: ScanConfig[];
  defaultScanConfig: string;
  autoScanOnStartup: boolean;
  maxDepth: number;
  excludeHidden: boolean;
}

@injectable()
export class ConfigService {
  private configPath: string;
  private config!: AppConfig;

  constructor() {
    // 使用用户主目录下的配置文件，避免代码更新时丢失
    const os = require('os');
    const userHome = os.homedir();
    const configDir = path.join(userHome, '.devworkbench');
    this.configPath = path.join(configDir, 'app-config.json');

    this.loadConfig();
  }

  private loadConfig(): void {
    try {
      // 确保配置目录存在
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }

      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf-8');
        this.config = JSON.parse(configData);

        // 转换日期字符串为Date对象
        this.config.scanConfigs.forEach(config => {
          config.createdAt = new Date(config.createdAt);
          config.updatedAt = new Date(config.updatedAt);
        });
      } else {
        // 创建默认配置
        this.config = this.createDefaultConfig();
        this.saveConfig();
      }
    } catch (error) {
      console.error('加载配置失败，使用默认配置:', error);
      this.config = this.createDefaultConfig();
    }
  }

  private createDefaultConfig(): AppConfig {
    const defaultScanConfig: ScanConfig = {
      id: 'default',
      name: '默认扫描配置（当前目录）',
      paths: [process.cwd()], // 使用绝对路径
      recursive: true,
      excludePatterns: [
        'node_modules',
        '.git',
        'dist',
        'build',
        '.next',
        '.nuxt',
        'target',
        'bin',
        'obj',
        '.vscode',
        '.idea'
      ],
      includePatterns: ['*'],
      enabled: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return {
      scanConfigs: [defaultScanConfig],
      defaultScanConfig: 'default',
      autoScanOnStartup: false,
      maxDepth: 5,
      excludeHidden: true
    };
  }

  private saveConfig(): void {
    try {
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }

      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('保存配置失败:', error);
      throw new Error('配置保存失败');
    }
  }

  // 获取所有配置
  getConfig(): AppConfig {
    return { ...this.config };
  }

  // 获取所有扫描配置
  getScanConfigs(): ScanConfig[] {
    return [...this.config.scanConfigs];
  }

  // 获取特定扫描配置
  getScanConfig(id: string): ScanConfig | undefined {
    return this.config.scanConfigs.find(config => config.id === id);
  }

  // 获取默认扫描配置
  getDefaultScanConfig(): ScanConfig | undefined {
    return this.getScanConfig(this.config.defaultScanConfig);
  }

  // 添加扫描配置
  addScanConfig(config: Omit<ScanConfig, 'id' | 'createdAt' | 'updatedAt'>): ScanConfig {
    const newConfig: ScanConfig = {
      ...config,
      id: this.generateId(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.config.scanConfigs.push(newConfig);
    this.saveConfig();
    return newConfig;
  }

  // 更新扫描配置
  updateScanConfig(id: string, updates: Partial<Omit<ScanConfig, 'id' | 'createdAt'>>): ScanConfig {
    const configIndex = this.config.scanConfigs.findIndex(config => config.id === id);
    if (configIndex === -1) {
      throw new Error('配置不存在');
    }

    this.config.scanConfigs[configIndex] = {
      ...this.config.scanConfigs[configIndex],
      ...updates,
      updatedAt: new Date()
    };

    this.saveConfig();
    return this.config.scanConfigs[configIndex];
  }

  // 删除扫描配置
  deleteScanConfig(id: string): boolean {
    if (id === this.config.defaultScanConfig) {
      throw new Error('不能删除默认配置');
    }

    const configIndex = this.config.scanConfigs.findIndex(config => config.id === id);
    if (configIndex === -1) {
      return false;
    }

    this.config.scanConfigs.splice(configIndex, 1);
    this.saveConfig();
    return true;
  }

  // 设置默认扫描配置
  setDefaultScanConfig(id: string): void {
    if (!this.getScanConfig(id)) {
      throw new Error('配置不存在');
    }

    this.config.defaultScanConfig = id;
    this.saveConfig();
  }

  // 更新应用配置
  updateAppConfig(updates: Partial<Omit<AppConfig, 'scanConfigs'>>): AppConfig {
    this.config = {
      ...this.config,
      ...updates
    };

    this.saveConfig();
    return this.config;
  }

  // 导出配置
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  // 导入配置
  importConfig(configJson: string): void {
    try {
      const importedConfig = JSON.parse(configJson);

      // 验证配置格式
      if (!importedConfig.scanConfigs || !Array.isArray(importedConfig.scanConfigs)) {
        throw new Error('无效的配置格式');
      }

      // 转换日期字符串
      importedConfig.scanConfigs.forEach((config: any) => {
        config.createdAt = new Date(config.createdAt);
        config.updatedAt = new Date(config.updatedAt);
      });

      this.config = importedConfig;
      this.saveConfig();
    } catch (error) {
      throw new Error('配置导入失败: ' + (error as Error).message);
    }
  }

  // 重置为默认配置
  resetToDefault(): void {
    this.config = this.createDefaultConfig();
    this.saveConfig();
  }

  // 生成唯一ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // 验证路径是否存在
  validatePaths(paths: string[]): { valid: string[], invalid: string[] } {
    const valid: string[] = [];
    const invalid: string[] = [];

    paths.forEach(p => {
      try {
        if (fs.existsSync(p)) {
          valid.push(p);
        } else {
          invalid.push(p);
        }
      } catch (error) {
        invalid.push(p);
      }
    });

    return { valid, invalid };
  }

  // 获取路径统计信息
  getPathStats(paths: string[]): { path: string, exists: boolean, type: string, size?: number, absolutePath?: string }[] {
    return paths.map(p => {
      try {
        const absolutePath = path.isAbsolute(p) ? p : path.resolve(p);
        if (fs.existsSync(absolutePath)) {
          const stats = fs.statSync(absolutePath);
          return {
            path: p,
            absolutePath,
            exists: true,
            type: stats.isDirectory() ? 'directory' : 'file',
            size: stats.isFile() ? stats.size : undefined
          };
        } else {
          return {
            path: p,
            absolutePath,
            exists: false,
            type: 'unknown'
          };
        }
      } catch (error) {
        return {
          path: p,
          absolutePath: path.isAbsolute(p) ? p : path.resolve(p),
          exists: false,
          type: 'error'
        };
      }
    });
  }

  // 规范化路径（转换为绝对路径）
  normalizePaths(paths: string[]): string[] {
    return paths.map(p => {
      if (path.isAbsolute(p)) {
        return path.normalize(p);
      } else {
        return path.resolve(p);
      }
    });
  }

  // 获取配置文件路径（用于显示给用户）
  getConfigFilePath(): string {
    return this.configPath;
  }

  // 获取配置目录路径
  getConfigDirectory(): string {
    return path.dirname(this.configPath);
  }

  // 检查配置文件是否存在
  configFileExists(): boolean {
    return fs.existsSync(this.configPath);
  }

  // 备份配置文件
  backupConfig(): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = this.configPath.replace('.json', `_backup_${timestamp}.json`);

    if (fs.existsSync(this.configPath)) {
      fs.copyFileSync(this.configPath, backupPath);
      return backupPath;
    }

    throw new Error('配置文件不存在，无法备份');
  }

  // 获取系统常用目录
  getCommonDirectories(): { name: string, path: string, description: string }[] {
    const os = require('os');
    const userHome = os.homedir();

    const commonDirs = [
      { name: '用户主目录', path: userHome, description: '当前用户的主目录' },
      { name: '桌面', path: path.join(userHome, 'Desktop'), description: '桌面文件夹' },
      { name: '文档', path: path.join(userHome, 'Documents'), description: '文档文件夹' },
      { name: '下载', path: path.join(userHome, 'Downloads'), description: '下载文件夹' },
      { name: '当前工作目录', path: process.cwd(), description: '程序当前运行目录' }
    ];

    // Windows特定目录
    if (process.platform === 'win32') {
      commonDirs.push(
        { name: 'C盘根目录', path: 'C:\\', description: 'C盘根目录' },
        { name: 'Program Files', path: 'C:\\Program Files', description: '程序安装目录' },
        { name: 'Program Files (x86)', path: 'C:\\Program Files (x86)', description: '32位程序安装目录' }
      );
    }

    // 过滤存在的目录
    return commonDirs.filter(dir => {
      try {
        return fs.existsSync(dir.path) && fs.statSync(dir.path).isDirectory();
      } catch {
        return false;
      }
    });
  }
}
