import "reflect-metadata";
import { container } from "../../src/main/core/container";
import { TYPES, IProjectDiscoveryService, IGitInfo, IVersionControlAdapter, IPersistenceService } from "../../src/shared/types";

describe("ProjectDiscoveryService Integration", () => {
  let projectDiscoveryService: IProjectDiscoveryService;
  let mockGitAdapter: jest.Mocked<IVersionControlAdapter>;
  let mockPersistenceService: jest.Mocked<IPersistenceService>;

  beforeEach(() => {
    // Create mocks for the dependencies
    mockGitAdapter = {
      isRepository: jest.fn(),
      getStatus: jest.fn(),
      pull: jest.fn(),
    };
    mockPersistenceService = {
      get: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
    };

    // Rebind the interfaces to our mock instances for this test
    container.rebind<IVersionControlAdapter>(TYPES.VersionControlAdapter).toConstantValue(mockGitAdapter);
    container.rebind<IPersistenceService>(TYPES.PersistenceService).toConstantValue(mockPersistenceService);

    // Get the real service, which will be injected with our mocks
    projectDiscoveryService = container.get<IProjectDiscoveryService>(TYPES.ProjectDiscoveryService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test("should use injected services to scan and save projects", async () => {
    // Arrange
    const mockGitInfo: IGitInfo = { branch: "main", ahead: 0, behind: 0, hasUncommittedChanges: false, hasUntrackedFiles: false };
    mockGitAdapter.isRepository.mockResolvedValue(true);
    mockGitAdapter.getStatus.mockResolvedValue(mockGitInfo);
    mockPersistenceService.get.mockResolvedValue([]); // Simulate no existing projects

    // Act: In a real test, we'd mock fs.readdir. Here we rely on the mocked isRepository.
    // We will test the logic, assuming the scan finds one repo at the root.
    await projectDiscoveryService.scanDirectory(".");

    // Assert
    expect(mockGitAdapter.isRepository).toHaveBeenCalledWith(".");
    expect(mockGitAdapter.getStatus).toHaveBeenCalledWith(".");
    expect(mockPersistenceService.set).toHaveBeenCalledWith("projects", expect.any(Array));

    const savedProjects = (mockPersistenceService.set as jest.Mock).mock.calls[0][1];
    expect(savedProjects).toHaveLength(1);
    expect(savedProjects[0].name).toBe("."); // Project name is the directory name
  });
});
