import { injectable, inject } from "inversify";
import * as fs from "fs";
import * as path from "path";
import { 
  IProjectDiscoveryService, 
  IProject, 
  ILogger, 
  IPersistenceService,
  IVersionControlAdapter,
  TYPES, 
  CoreEvents, 
  IEventBus,
  ProjectType
} from "../../shared/types";

@injectable()
export class ProjectDiscoveryService implements IProjectDiscoveryService {
  constructor(
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.PersistenceService) private persistence: IPersistenceService,
    @inject(TYPES.VersionControlAdapter) private gitAdapter: IVersionControlAdapter,
    @inject(TYPES.EventBus) private eventBus: IEventBus
  ) {}

  async scanDirectory(scanPath: string): Promise<IProject[]> {
    this.logger.info(`Scanning directory: ${scanPath}`);
    const discoveredProjects: IProject[] = [];
    await this.scanDirectoryRecursive(scanPath, discoveredProjects, 0, 5);
    
    const existingProjects = await this.getProjects();
    const existingProjectPaths = new Set(existingProjects.map(p => p.path));
    
    const newProjects = discoveredProjects.filter(p => !existingProjectPaths.has(p.path));

    if (newProjects.length > 0) {
        const allProjects = [...existingProjects, ...newProjects];
        await this.persistence.set("projects", allProjects);
        newProjects.forEach(p => this.eventBus.emitEvent(CoreEvents.PROJECT_DISCOVERED, p));
        this.logger.info(`Discovered and saved ${newProjects.length} new projects.`);
    }
    
    return this.getProjects();
  }

  async getProjects(): Promise<IProject[]> {
    return (await this.persistence.get<IProject[]>("projects")) || [];
  }

  async refreshProject(projectId: string): Promise<void> {
    const projects = await this.getProjects();
    const projectIndex = projects.findIndex(p => p.id === projectId);
    if (projectIndex === -1) {
      this.logger.warn(`Project with ID ${projectId} not found for refresh.`);
      return;
    }
    
    const project = projects[projectIndex];
    project.gitInfo = await this.gitAdapter.getStatus(project.path);
    project.lastAccessed = new Date();
    projects[projectIndex] = project;

    await this.persistence.set("projects", projects);
    this.eventBus.emitEvent(CoreEvents.PROJECT_UPDATED, project);
    this.logger.info(`Refreshed project: ${project.name}`);
  }

  private async scanDirectoryRecursive(dirPath: string, projects: IProject[], depth: number, maxDepth: number): Promise<void> {
    if (depth > maxDepth) return;
    
    const skipDirs = ["node_modules", ".git", "dist", "build", "target"];
    if (await this.gitAdapter.isRepository(dirPath)) {
      const project = await this.createProjectFromPath(dirPath);
      projects.push(project);
      return; // Stop scanning deeper once a repo is found
    }

    try {
        const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
        for (const entry of entries) {
          if (entry.isDirectory() && !skipDirs.includes(entry.name)) {
            await this.scanDirectoryRecursive(path.join(dirPath, entry.name), projects, depth + 1, maxDepth);
          }
        }
    } catch (error) {
        this.logger.debug(`Could not read directory: ${dirPath}`, error);
    }
  }

  private async createProjectFromPath(projectPath: string): Promise<IProject> {
    const projectType = fs.existsSync(path.join(projectPath, "package.json")) ? ProjectType.NODE_JS : ProjectType.UNKNOWN;
    
    return {
      id: `proj_${Buffer.from(projectPath).toString("hex")}`,
      name: path.basename(projectPath),
      path: projectPath,
      type: projectType,
      gitInfo: await this.gitAdapter.getStatus(projectPath),
      lastAccessed: new Date(),
    };
  }
}
