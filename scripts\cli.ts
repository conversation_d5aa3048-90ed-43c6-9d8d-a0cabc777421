#!/usr/bin/env node
/**
 * DevWorkbench CLI - 命令行界面
 * 提供完整的命令行操作接口
 */

import 'reflect-metadata';
import { Container } from 'inversify';
import { EventBus } from '../src/main/core/event-bus';
import { SimpleLogger } from '../src/main/services/logger-simple.service';
import { SimpleStateRepository } from '../src/main/services/state-simple.service';
import { ProjectDiscoveryService } from '../src/main/services/project-discovery.service';
import { WorkflowService } from '../src/main/services/workflow.service';
import { ServiceOrchestrator } from '../src/main/services/service-orchestrator.service';
import { HealthCheckService } from '../src/main/services/health-check.service';
import { BatchOperationsService } from '../src/main/services/batch-operations.service';
import { GitAdapter } from '../src/main/plugins/git/git-adapter';
import { ProcessLauncher } from '../src/main/services/process-launcher.service';
import { PortMonitor } from '../src/main/services/port-monitor.service';
import { TYPES, IProject, IWorkflowGroup } from '../src/shared/types';

class DevWorkbenchCLI {
  private container: Container;
  private projectService!: ProjectDiscoveryService;
  private workflowService!: WorkflowService;
  private orchestrator!: ServiceOrchestrator;
  private batchOps!: BatchOperationsService;

  constructor() {
    this.container = new Container();
    this.setupContainer();
    this.initializeServices();
  }

  private setupContainer() {
    // 注册所有服务
    this.container.bind(TYPES.EventBus).to(EventBus).inSingletonScope();
    this.container.bind(TYPES.Logger).to(SimpleLogger).inSingletonScope();
    this.container.bind(TYPES.StateRepository).to(SimpleStateRepository).inSingletonScope();
    this.container.bind(TYPES.VersionControlAdapter).to(GitAdapter).inSingletonScope();
    this.container.bind(TYPES.ProjectDiscoveryService).to(ProjectDiscoveryService).inSingletonScope();
    this.container.bind(TYPES.ProcessLauncher).to(ProcessLauncher).inSingletonScope();
    this.container.bind(TYPES.PortMonitor).to(PortMonitor).inSingletonScope();
    this.container.bind(TYPES.WorkflowService).to(WorkflowService).inSingletonScope();
    this.container.bind(TYPES.ServiceOrchestrator).to(ServiceOrchestrator).inSingletonScope();
    this.container.bind(TYPES.HealthCheckService).to(HealthCheckService).inSingletonScope();
    this.container.bind(TYPES.BatchOperationsService).to(BatchOperationsService).inSingletonScope();
  }

  private initializeServices() {
    this.projectService = this.container.get<ProjectDiscoveryService>(TYPES.ProjectDiscoveryService);
    this.workflowService = this.container.get<WorkflowService>(TYPES.WorkflowService);
    this.orchestrator = this.container.get<ServiceOrchestrator>(TYPES.ServiceOrchestrator);
    this.batchOps = this.container.get<BatchOperationsService>(TYPES.BatchOperationsService);
  }

  async run() {
    const args = process.argv.slice(2);

    if (args.length === 0) {
      this.showHelp();
      return;
    }

    const command = args[0];
    const subCommand = args[1];
    const options = args.slice(2);

    try {
      switch (command) {
        case 'scan':
          await this.handleScan(options[0] || '.');
          break;
        case 'list':
          await this.handleList();
          break;
        case 'open':
          await this.handleOpen(subCommand, options);
          break;
        case 'git':
          await this.handleGit(subCommand, options);
          break;
        case 'workflow':
          await this.handleWorkflow(subCommand, options);
          break;
        case 'batch':
          await this.handleBatch(subCommand, options);
          break;
        case 'monitor':
          await this.handleMonitor(subCommand, options);
          break;
        case 'help':
        case '--help':
        case '-h':
          this.showHelp();
          break;
        default:
          console.log(`❌ 未知命令: ${command}`);
          this.showHelp();
      }
    } catch (error) {
      console.error('❌ 执行失败:', (error as Error).message);
      process.exit(1);
    }
  }

  private async handleScan(path: string) {
    console.log(`🔍 扫描目录: ${path}`);
    const projects = await this.projectService.scanDirectory(path);
    console.log(`✅ 发现 ${projects.length} 个项目`);

    projects.forEach(project => {
      console.log(`📦 ${project.name} (${project.type}) - ${project.path}`);
      if (project.gitInfo) {
        console.log(`   🌿 分支: ${project.gitInfo.branch}`);
        if (project.gitInfo.hasUncommittedChanges) {
          console.log(`   ⚠️  有未提交更改`);
        }
      }
    });
  }

  private async handleList() {
    console.log('📋 项目列表:');
    const projects = this.projectService.getProjects();

    if (projects.length === 0) {
      console.log('   没有发现项目，请先运行 scan 命令');
      return;
    }

    projects.forEach((project, index) => {
      console.log(`${index + 1}. ${project.name} (${project.type})`);
      console.log(`   📁 ${project.path}`);
      if (project.gitInfo) {
        console.log(`   🌿 ${project.gitInfo.branch}${project.gitInfo.hasUncommittedChanges ? ' (有更改)' : ''}`);
      }
      console.log('');
    });
  }

  private async handleOpen(projectName: string, options: string[]) {
    if (!projectName) {
      console.log('❌ 请指定项目名称');
      return;
    }

    const projects = this.projectService.getProjects();
    const project = projects.find(p => p.name === projectName || p.id === projectName);

    if (!project) {
      console.log(`❌ 未找到项目: ${projectName}`);
      return;
    }

    const action = options[0] || 'vscode';

    switch (action) {
      case '--vscode':
      case 'vscode':
        console.log(`🚀 在VS Code中打开: ${project.name}`);
        // 实际实现会调用 code 命令
        break;
      case '--terminal':
      case 'terminal':
        console.log(`🖥️  在终端中打开: ${project.name}`);
        // 实际实现会打开终端
        break;
      case '--explorer':
      case 'explorer':
        console.log(`📁 在文件管理器中打开: ${project.name}`);
        // 实际实现会打开文件管理器
        break;
      default:
        console.log(`❌ 未知操作: ${action}`);
    }
  }

  private async handleGit(operation: string, options: string[]) {
    const projects = this.projectService.getProjects();
    const projectName = options[0];

    if (projectName) {
      const project = projects.find(p => p.name === projectName);
      if (!project) {
        console.log(`❌ 未找到项目: ${projectName}`);
        return;
      }

      switch (operation) {
        case 'status':
          console.log(`📊 Git状态: ${project.name}`);
          if (project.gitInfo) {
            console.log(`   分支: ${project.gitInfo.branch}`);
            console.log(`   状态: ${project.gitInfo.hasUncommittedChanges ? '有更改' : '干净'}`);
          }
          break;
        case 'pull':
          console.log(`⬇️  Git Pull: ${project.name}`);
          // 实际实现会执行git pull
          break;
      }
    } else {
      // 对所有项目执行操作
      console.log(`🔄 对所有项目执行 git ${operation}...`);
      const projectIds = projects.map(p => p.id);

      switch (operation) {
        case 'status':
          const result = await this.batchOps.batchGitStatus(projectIds);
          console.log(`✅ 完成: 成功 ${result.successCount}, 失败 ${result.failureCount}`);
          break;
        case 'pull':
          const pullResult = await this.batchOps.batchGitPull(projectIds);
          console.log(`✅ 完成: 成功 ${pullResult.successCount}, 失败 ${pullResult.failureCount}`);
          break;
      }
    }
  }

  private async handleWorkflow(operation: string, options: string[]) {
    switch (operation) {
      case 'list':
        const groups = await this.workflowService.getWorkflowGroups();
        console.log('🔄 工作流组列表:');
        groups.forEach((group, index) => {
          console.log(`${index + 1}. ${group.name} (${group.services.length} 个服务)`);
          console.log(`   📝 ${group.description}`);
        });
        break;

      case 'start':
        const groupName = options[0];
        if (!groupName) {
          console.log('❌ 请指定工作流组名称');
          return;
        }

        const groups2 = await this.workflowService.getWorkflowGroups();
        const group = groups2.find(g => g.name === groupName);
        if (!group) {
          console.log(`❌ 未找到工作流组: ${groupName}`);
          return;
        }

        console.log(`🚀 启动工作流组: ${group.name}`);
        const execution = await this.orchestrator.startWorkflowGroup(group);
        console.log(`✅ 启动成功: ${execution.id}`);
        break;

      case 'stop':
        const executions = this.orchestrator.getActiveExecutions();
        if (executions.length === 0) {
          console.log('ℹ️  没有运行中的工作流');
          return;
        }

        console.log('⏹️  停止所有工作流...');
        await this.orchestrator.stopAllExecutions();
        console.log('✅ 所有工作流已停止');
        break;

      default:
        console.log(`❌ 未知工作流操作: ${operation}`);
    }
  }

  private async handleBatch(operation: string, options: string[]) {
    const projects = this.projectService.getProjects();
    const projectIds = projects.map(p => p.id);

    if (projectIds.length === 0) {
      console.log('❌ 没有项目，请先扫描项目');
      return;
    }

    console.log(`🔄 批量操作: ${operation} (${projectIds.length} 个项目)`);

    switch (operation) {
      case 'git-pull':
        const result = await this.batchOps.batchGitPull(projectIds);
        console.log(`✅ Git Pull 完成: 成功 ${result.successCount}, 失败 ${result.failureCount}`);
        break;
      case 'git-status':
        const statusResult = await this.batchOps.batchGitStatus(projectIds);
        console.log(`✅ Git Status 完成: 成功 ${statusResult.successCount}, 失败 ${statusResult.failureCount}`);
        break;
      case 'open-vscode':
        const vscodeResult = await this.batchOps.batchOpenInVSCode(projectIds);
        console.log(`✅ VS Code 打开完成: 成功 ${vscodeResult.successCount}, 失败 ${vscodeResult.failureCount}`);
        break;
      default:
        console.log(`❌ 未知批量操作: ${operation}`);
    }
  }

  private async handleMonitor(type: string, options: string[]) {
    switch (type) {
      case 'ports':
        console.log('🔌 端口监控功能 (演示模式)');
        console.log('监控常用端口: 3000, 3001, 8080...');
        break;
      case 'health':
        console.log('🏥 健康检查功能 (演示模式)');
        console.log('监控服务健康状态...');
        break;
      default:
        console.log(`❌ 未知监控类型: ${type}`);
    }
  }

  private showHelp() {
    console.log(`
🚀 DevWorkbench CLI - 开发环境管理工具

用法: devworkbench <命令> [选项]

命令:
  scan [path]              扫描项目目录 (默认: 当前目录)
  list                     列出所有项目
  open <project> [action]  打开项目 (action: vscode|terminal|explorer)
  git <operation> [project] Git操作 (operation: status|pull)
  workflow <operation>     工作流管理 (operation: list|start|stop)
  batch <operation>        批量操作 (operation: git-pull|git-status|open-vscode)
  monitor <type>           监控功能 (type: ports|health)
  help                     显示帮助信息

示例:
  devworkbench scan .                    # 扫描当前目录
  devworkbench list                      # 列出所有项目
  devworkbench open myproject vscode     # 在VS Code中打开项目
  devworkbench git pull                  # 对所有项目执行git pull
  devworkbench workflow list             # 列出工作流组
  devworkbench batch git-pull            # 批量git pull

更多信息请访问: https://github.com/your-repo/devworkbench
`);
  }
}

// 运行CLI
if (require.main === module) {
  const cli = new DevWorkbenchCLI();
  cli.run().catch(console.error);
}

export { DevWorkbenchCLI };
