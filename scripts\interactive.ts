#!/usr/bin/env node
/**
 * DevWorkbench Interactive - 交互式界面
 * 提供菜单驱动的用户界面
 */

import 'reflect-metadata';
import * as readline from 'readline';
import { Container } from 'inversify';
import { EventBus } from '../src/main/core/event-bus';
import { SimpleLogger } from '../src/main/services/logger-simple.service';
import { SimpleStateRepository } from '../src/main/services/state-simple.service';
import { ProjectDiscoveryService } from '../src/main/services/project-discovery.service';
import { WorkflowService } from '../src/main/services/workflow.service';
import { ServiceOrchestrator } from '../src/main/services/service-orchestrator.service';
import { HealthCheckService } from '../src/main/services/health-check.service';
import { BatchOperationsService } from '../src/main/services/batch-operations.service';
import { GitAdapter } from '../src/main/plugins/git/git-adapter';
import { ProcessLauncher } from '../src/main/services/process-launcher.service';
import { PortMonitor } from '../src/main/services/port-monitor.service';
import { TYPES, IProject, IWorkflowGroup } from '../src/shared/types';

class DevWorkbenchInteractive {
  private container: Container;
  private projectService!: ProjectDiscoveryService;
  private workflowService!: WorkflowService;
  private orchestrator!: ServiceOrchestrator;
  private batchOps!: BatchOperationsService;
  private rl!: readline.Interface;

  constructor() {
    this.container = new Container();
    this.setupContainer();
    this.initializeServices();
    this.setupReadline();
  }

  private setupContainer() {
    // 注册所有服务
    this.container.bind(TYPES.EventBus).to(EventBus).inSingletonScope();
    this.container.bind(TYPES.Logger).to(SimpleLogger).inSingletonScope();
    this.container.bind(TYPES.StateRepository).to(SimpleStateRepository).inSingletonScope();
    this.container.bind(TYPES.VersionControlAdapter).to(GitAdapter).inSingletonScope();
    this.container.bind(TYPES.ProjectDiscoveryService).to(ProjectDiscoveryService).inSingletonScope();
    this.container.bind(TYPES.ProcessLauncher).to(ProcessLauncher).inSingletonScope();
    this.container.bind(TYPES.PortMonitor).to(PortMonitor).inSingletonScope();
    this.container.bind(TYPES.WorkflowService).to(WorkflowService).inSingletonScope();
    this.container.bind(TYPES.ServiceOrchestrator).to(ServiceOrchestrator).inSingletonScope();
    this.container.bind(TYPES.HealthCheckService).to(HealthCheckService).inSingletonScope();
    this.container.bind(TYPES.BatchOperationsService).to(BatchOperationsService).inSingletonScope();
  }

  private initializeServices() {
    this.projectService = this.container.get<ProjectDiscoveryService>(TYPES.ProjectDiscoveryService);
    this.workflowService = this.container.get<WorkflowService>(TYPES.WorkflowService);
    this.orchestrator = this.container.get<ServiceOrchestrator>(TYPES.ServiceOrchestrator);
    this.batchOps = this.container.get<BatchOperationsService>(TYPES.BatchOperationsService);
  }

  private setupReadline() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  async run() {
    console.log(`
🚀 欢迎使用 DevWorkbench Interactive
=====================================
开发环境管理工具 - 交互式界面
`);

    await this.showMainMenu();
  }

  private async showMainMenu() {
    while (true) {
      console.log(`
📋 主菜单:
1. 📦 项目管理
2. 🔄 工作流管理
3. 📊 批量操作
4. 🔍 监控功能
5. ⚙️  系统设置
0. 🚪 退出

请选择操作 (0-5):`);

      const choice = await this.getUserInput('> ');

      switch (choice.trim()) {
        case '1':
          await this.showProjectMenu();
          break;
        case '2':
          await this.showWorkflowMenu();
          break;
        case '3':
          await this.showBatchMenu();
          break;
        case '4':
          await this.showMonitorMenu();
          break;
        case '5':
          await this.showSettingsMenu();
          break;
        case '0':
          console.log('👋 再见！');
          this.rl.close();
          return;
        default:
          console.log('❌ 无效选择，请重试');
      }
    }
  }

  private async showProjectMenu() {
    while (true) {
      console.log(`
📦 项目管理:
1. 🔍 扫描项目
2. 📋 列出项目
3. 🚀 打开项目
4. 🌿 Git操作
0. ⬅️  返回主菜单

请选择操作 (0-4):`);

      const choice = await this.getUserInput('> ');

      switch (choice.trim()) {
        case '1':
          await this.scanProjects();
          break;
        case '2':
          await this.listProjects();
          break;
        case '3':
          await this.openProject();
          break;
        case '4':
          await this.gitOperations();
          break;
        case '0':
          return;
        default:
          console.log('❌ 无效选择，请重试');
      }
    }
  }

  private async showWorkflowMenu() {
    while (true) {
      console.log(`
🔄 工作流管理:
1. 📋 列出工作流组
2. 🚀 启动工作流
3. ⏹️  停止工作流
4. 📊 查看状态
0. ⬅️  返回主菜单

请选择操作 (0-4):`);

      const choice = await this.getUserInput('> ');

      switch (choice.trim()) {
        case '1':
          await this.listWorkflows();
          break;
        case '2':
          await this.startWorkflow();
          break;
        case '3':
          await this.stopWorkflow();
          break;
        case '4':
          await this.showWorkflowStatus();
          break;
        case '0':
          return;
        default:
          console.log('❌ 无效选择，请重试');
      }
    }
  }

  private async showBatchMenu() {
    while (true) {
      console.log(`
📊 批量操作:
1. 🔄 批量Git Pull
2. 📊 批量Git Status
3. 🚀 批量打开VS Code
4. 🔄 批量刷新项目
0. ⬅️  返回主菜单

请选择操作 (0-4):`);

      const choice = await this.getUserInput('> ');

      switch (choice.trim()) {
        case '1':
          await this.batchGitPull();
          break;
        case '2':
          await this.batchGitStatus();
          break;
        case '3':
          await this.batchOpenVSCode();
          break;
        case '4':
          await this.batchRefresh();
          break;
        case '0':
          return;
        default:
          console.log('❌ 无效选择，请重试');
      }
    }
  }

  private async showMonitorMenu() {
    console.log(`
🔍 监控功能:
1. 🔌 端口监控 (演示模式)
2. 🏥 健康检查 (演示模式)

这些功能在后台运行，监控系统状态。
`);
    await this.getUserInput('按回车键返回主菜单...');
  }

  private async showSettingsMenu() {
    console.log(`
⚙️ 系统设置:
- 配置文件位置: ~/.devworkbench/
- 日志文件: test.log
- 状态存储: 内存 + 文件

当前系统状态正常 ✅
`);
    await this.getUserInput('按回车键返回主菜单...');
  }

  // 项目管理功能实现
  private async scanProjects() {
    const path = await this.getUserInput('请输入扫描路径 (默认: 当前目录): ') || '.';
    console.log(`🔍 扫描目录: ${path}`);

    try {
      const projects = await this.projectService.scanDirectory(path);
      console.log(`✅ 发现 ${projects.length} 个项目`);

      projects.forEach(project => {
        console.log(`📦 ${project.name} (${project.type}) - ${project.path}`);
      });
    } catch (error) {
      console.log(`❌ 扫描失败: ${(error as Error).message}`);
    }
  }

  private async listProjects() {
    console.log('📋 项目列表:');
    const projects = this.projectService.getProjects();

    if (projects.length === 0) {
      console.log('   没有发现项目，请先扫描项目');
      return;
    }

    projects.forEach((project, index) => {
      console.log(`${index + 1}. ${project.name} (${project.type})`);
      console.log(`   📁 ${project.path}`);
      if (project.gitInfo) {
        console.log(`   🌿 ${project.gitInfo.branch}${project.gitInfo.hasUncommittedChanges ? ' (有更改)' : ''}`);
      }
    });
  }

  private async openProject() {
    const projects = this.projectService.getProjects();
    if (projects.length === 0) {
      console.log('❌ 没有项目，请先扫描项目');
      return;
    }

    console.log('选择要打开的项目:');
    projects.forEach((project, index) => {
      console.log(`${index + 1}. ${project.name}`);
    });

    const choice = await this.getUserInput('请输入项目编号: ');
    const projectIndex = parseInt(choice) - 1;

    if (projectIndex < 0 || projectIndex >= projects.length) {
      console.log('❌ 无效的项目编号');
      return;
    }

    const project = projects[projectIndex];
    console.log(`🚀 打开项目: ${project.name} (模拟)`);
  }

  private async gitOperations() {
    console.log(`
🌿 Git操作:
1. 📊 查看状态
2. ⬇️  执行Pull
`);

    const choice = await this.getUserInput('请选择操作 (1-2): ');
    const projects = this.projectService.getProjects();

    if (projects.length === 0) {
      console.log('❌ 没有项目，请先扫描项目');
      return;
    }

    switch (choice.trim()) {
      case '1':
        console.log('📊 Git状态检查...');
        projects.forEach(project => {
          if (project.gitInfo) {
            console.log(`${project.name}: ${project.gitInfo.branch}${project.gitInfo.hasUncommittedChanges ? ' (有更改)' : ''}`);
          }
        });
        break;
      case '2':
        console.log('⬇️  执行Git Pull (模拟)...');
        console.log('✅ 所有项目Pull完成');
        break;
    }
  }

  // 工作流管理功能实现
  private async listWorkflows() {
    const groups = await this.workflowService.getWorkflowGroups();
    console.log('🔄 工作流组列表:');

    if (groups.length === 0) {
      console.log('   没有工作流组');
      return;
    }

    groups.forEach((group, index) => {
      console.log(`${index + 1}. ${group.name} (${group.services.length} 个服务)`);
      console.log(`   📝 ${group.description}`);
    });
  }

  private async startWorkflow() {
    const groups = await this.workflowService.getWorkflowGroups();
    if (groups.length === 0) {
      console.log('❌ 没有工作流组');
      return;
    }

    console.log('选择要启动的工作流组:');
    groups.forEach((group, index) => {
      console.log(`${index + 1}. ${group.name}`);
    });

    const choice = await this.getUserInput('请输入工作流组编号: ');
    const groupIndex = parseInt(choice) - 1;

    if (groupIndex < 0 || groupIndex >= groups.length) {
      console.log('❌ 无效的工作流组编号');
      return;
    }

    const group = groups[groupIndex];
    console.log(`🚀 启动工作流组: ${group.name} (模拟)`);
  }

  private async stopWorkflow() {
    const executions = this.orchestrator.getActiveExecutions();
    if (executions.length === 0) {
      console.log('ℹ️  没有运行中的工作流');
      return;
    }

    console.log('⏹️  停止所有工作流 (模拟)...');
    console.log('✅ 所有工作流已停止');
  }

  private async showWorkflowStatus() {
    const executions = this.orchestrator.getActiveExecutions();
    console.log(`📊 当前运行中的工作流: ${executions.length} 个`);
  }

  // 批量操作功能实现
  private async batchGitPull() {
    const projects = this.projectService.getProjects();
    if (projects.length === 0) {
      console.log('❌ 没有项目，请先扫描项目');
      return;
    }

    console.log(`🔄 批量Git Pull (${projects.length} 个项目)...`);
    console.log('✅ 批量操作完成 (模拟)');
  }

  private async batchGitStatus() {
    const projects = this.projectService.getProjects();
    if (projects.length === 0) {
      console.log('❌ 没有项目，请先扫描项目');
      return;
    }

    console.log(`📊 批量Git Status (${projects.length} 个项目)...`);
    console.log('✅ 批量状态检查完成 (模拟)');
  }

  private async batchOpenVSCode() {
    const projects = this.projectService.getProjects();
    if (projects.length === 0) {
      console.log('❌ 没有项目，请先扫描项目');
      return;
    }

    console.log(`🚀 批量打开VS Code (${projects.length} 个项目)...`);
    console.log('✅ 批量打开完成 (模拟)');
  }

  private async batchRefresh() {
    const projects = this.projectService.getProjects();
    if (projects.length === 0) {
      console.log('❌ 没有项目，请先扫描项目');
      return;
    }

    console.log(`🔄 批量刷新项目 (${projects.length} 个项目)...`);
    console.log('✅ 批量刷新完成 (模拟)');
  }

  private getUserInput(prompt: string): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question(prompt, (answer) => {
        resolve(answer);
      });
    });
  }
}

// 运行交互式界面
if (require.main === module) {
  const interactive = new DevWorkbenchInteractive();
  interactive.run().catch(console.error);
}

export { DevWorkbenchInteractive };
