import { injectable, inject } from "inversify";
import * as net from "net";
import { IPortMonitor, IWatcher, ILogger, TYPES, IEventBus, CoreEvents } from "../../shared/types";

@injectable()
export class PortMonitor implements IPortMonitor {
  constructor(
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.EventBus) private eventBus: IEventBus
  ) { }

  checkPort(port: number): Promise<boolean> {
    return new Promise((resolve) => {
      const socket = new net.Socket();

      const timeout = setTimeout(() => {
        socket.destroy();
        resolve(true); // Timeout means port is free
      }, 1000);

      socket.connect(port, "127.0.0.1", () => {
        clearTimeout(timeout);
        socket.destroy();
        resolve(false); // Connection successful, port is in use
      });

      socket.on("error", () => {
        clearTimeout(timeout);
        resolve(true); // Connection failed, port is free
      });
    });
  }

  watch(port: number, callback: (isUsed: boolean) => void): IWatcher {
    // For now, this is a simplified implementation. A real one would use polling.
    this.logger.warn("PortMonitor.watch is a simplified implementation for this refactoring phase.");
    const interval = setInterval(async () => {
      const isFree = await this.checkPort(port);
      const isUsed = !isFree;
      callback(isUsed);
      this.eventBus.emitEvent(isUsed ? CoreEvents.PORT_UNAVAILABLE : CoreEvents.PORT_AVAILABLE, { port });
    }, 5000);

    return {
      stop: () => clearInterval(interval)
    };
  }
}
