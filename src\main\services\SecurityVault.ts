import { injectable } from "inversify";
import { ISecurityVault } from "../../shared/types";

// 优雅降级：在非 Electron 环境中使用 mock
let safeStorage: any;
try {
    safeStorage = require("electron").safeStorage;
} catch {
    safeStorage = null;
}

@injectable()
export class SecurityVault implements ISecurityVault {
    public isEncryptionAvailable(): boolean {
        // 在Node.js后端环境中，我们假设safeStorage不可用，优雅降级
        // 在真实的Electron主进程中，这将返回正确的值
        try {
            return safeStorage && safeStorage.isEncryptionAvailable();
        } catch {
            return false;
        }
    }

    async store(key: string, value: string): Promise<void> {
        // 优雅降级：在非Electron环境中，我们不执行任何操作
        console.warn(`SecurityVault: Storing ${key} is a no-op in this environment.`);
    }

    async retrieve(key: string): Promise<string | null> {
        console.warn(`SecurityVault: Retrieving ${key} is a no-op in this environment.`);
        return null;
    }

    async remove(key: string): Promise<void> {
        console.warn(`SecurityVault: Removing ${key} is a no-op in this environment.`);
    }
}
