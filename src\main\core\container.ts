/**
 * 依赖注入容器配置
 * 使用 InversifyJS 实现依赖注入
 */

import { Container } from "inversify";
import { TYPES, IEventBus, ILogger, IPersistenceService, ISecurityVault } from "../../shared/types";
import { EventBus } from "./event-bus";
import { Logger } from "../services/Logger";
import { PersistenceService } from "../services/PersistenceService";
import { SecurityVault } from "../services/SecurityVault";

const container = new Container();

// 核心服务绑定
container.bind<IEventBus>(TYPES.EventBus).to(EventBus).inSingletonScope();
container.bind<ILogger>(TYPES.Logger).to(Logger).inSingletonScope();
container.bind<IPersistenceService>(TYPES.PersistenceService).to(PersistenceService).inSingletonScope();
container.bind<ISecurityVault>(TYPES.SecurityVault).to(SecurityVault).inSingletonScope();

export { container };
