/**
 * 依赖注入容器配置
 * 使用 InversifyJS 实现依赖注入
 */

import { Container } from "inversify";
import { TYPES, IEventBus, ILogger, IPersistenceService, ISecurityVault, IVersionControlAdapter, IProjectDiscoveryService, IProcessLauncher, IPortMonitor } from "../../shared/types";
import { EventBus } from "./event-bus";
import { Logger } from "../services/Logger";
import { PersistenceService } from "../services/PersistenceService";
import { SecurityVault } from "../services/SecurityVault";
import { GitAdapter } from "../plugins/git/git-adapter";
import { ProjectDiscoveryService } from "../services/ProjectDiscoveryService";
import { ProcessLauncher } from "../services/ProcessLauncher";
import { PortMonitor } from "../services/PortMonitor";

const container = new Container();

// 核心服务绑定
container.bind<IEventBus>(TYPES.EventBus).to(EventBus).inSingletonScope();
container.bind<ILogger>(TYPES.Logger).to(Logger).inSingletonScope();
container.bind<IPersistenceService>(TYPES.PersistenceService).to(PersistenceService).inSingletonScope();
container.bind<ISecurityVault>(TYPES.SecurityVault).to(SecurityVault).inSingletonScope();

// 业务服务绑定
container.bind<IVersionControlAdapter>(TYPES.VersionControlAdapter).to(GitAdapter).inSingletonScope();
container.bind<IProjectDiscoveryService>(TYPES.ProjectDiscoveryService).to(ProjectDiscoveryService).inSingletonScope();
container.bind<IProcessLauncher>(TYPES.ProcessLauncher).to(ProcessLauncher).inSingletonScope();
container.bind<IPortMonitor>(TYPES.PortMonitor).to(PortMonitor).inSingletonScope();

export { container };
