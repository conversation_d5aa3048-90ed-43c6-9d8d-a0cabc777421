import "reflect-metadata";
import { container } from "../../src/main/core/container";
import { TYPES, IProcessLauncher, IPortMonitor, IEventBus, CoreEvents, ProcessConfig } from "../../src/shared/types";
import * as net from "net";

describe("Execution Services Integration", () => {
  let processLauncher: IProcessLauncher;
  let portMonitor: IPortMonitor;
  let eventBus: IEventBus;
  let eventBusEmitSpy: jest.SpyInstance;

  beforeEach(() => {
    // We get real instances from the container
    processLauncher = container.get<IProcessLauncher>(TYPES.ProcessLauncher);
    portMonitor = container.get<IPortMonitor>(TYPES.PortMonitor);
    eventBus = container.get<IEventBus>(TYPES.EventBus);
    eventBusEmitSpy = jest.spyOn(eventBus, "emitEvent");
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test("ProcessLauncher should start a process and emit events", async () => {
    // Arrange
    const config: ProcessConfig = {
      id: "test-proc-1",
      name: "Test Echo",
      command: "echo",
      args: ["hello world"],
      cwd: ".",
    };

    // Act
    const handle = await processLauncher.start(config);
    
    // Assert
    expect(handle).toBeDefined();
    expect(handle.pid).toBeGreaterThan(0);

    // Wait for events to fire
    await new Promise(resolve => setTimeout(resolve, 500));
    
    expect(eventBusEmitSpy).toHaveBeenCalledWith(CoreEvents.PROCESS_STARTED, expect.any(Object));
    expect(eventBusEmitSpy).toHaveBeenCalledWith(CoreEvents.PROCESS_OUTPUT, expect.objectContaining({ data: expect.stringContaining("hello world") }));
    expect(eventBusEmitSpy).toHaveBeenCalledWith(CoreEvents.PROCESS_STOPPED, expect.any(Object));

    await processLauncher.stopAll(); // Cleanup
  }, 10000); // 10s timeout

  test("PortMonitor should correctly identify a used port", (done) => {
    // Arrange
    const testPort = 54321;
    const server = net.createServer().listen(testPort, async () => {
        // Act
        const isFree = await portMonitor.checkPort(testPort);

        // Assert
        expect(isFree).toBe(false);

        server.close(() => done());
    });
  });
});
