/**
 * Git适配器实现
 * 基于 simple-git 的版本控制操作
 */

import { injectable, inject } from "inversify";
import { simpleGit } from "simple-git";
import { IVersionControlAdapter, IGitInfo, ILogger, TYPES } from "../../../shared/types";
import * as fs from "fs";
import * as path from "path";

@injectable()
export class GitAdapter implements IVersionControlAdapter {
  constructor(@inject(TYPES.Logger) private logger: ILogger) { }

  async isRepository(repoPath: string): Promise<boolean> {
    try {
      const gitDir = path.join(repoPath, ".git");
      return fs.existsSync(gitDir);
    } catch (error) {
      this.logger.debug(`Git repository check failed for: ${repoPath}`, error);
      return false;
    }
  }

  async getStatus(repoPath: string): Promise<IGitInfo> {
    try {
      const git = simpleGit(repoPath);
      const status = await git.status();
      const branch = status.current || "unknown";
      return {
        branch,
        ahead: status.ahead,
        behind: status.behind,
        hasUncommittedChanges: status.files.length > 0,
        hasUntrackedFiles: status.not_added.length > 0,
      };
    } catch (error) {
      this.logger.error(`Failed to get Git status for: ${repoPath}`, error as Error);
      throw error;
    }
  }

  async pull(repoPath: string): Promise<void> {
    try {
      this.logger.info(`Executing git pull for: ${repoPath}`);
      const git = simpleGit(repoPath);
      await git.pull();
    } catch (error) {
      this.logger.error(`Git pull failed for: ${repoPath}`, error as Error);
      throw error;
    }
  }
}
