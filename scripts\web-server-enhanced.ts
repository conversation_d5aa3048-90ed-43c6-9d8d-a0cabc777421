#!/usr/bin/env node
/**
 * DevWorkbench Enhanced Web Server - 增强版Web界面
 * 提供完整的文件管理、项目控制和n8n集成功能
 */

import 'reflect-metadata';
import express from 'express';
import * as path from 'path';
import * as http from 'http';
import { Container } from 'inversify';
import { EventBus } from '../src/main/core/event-bus';
import { SimpleLogger } from '../src/main/services/logger-simple.service';
import { SimpleStateRepository } from '../src/main/services/state-simple.service';
import { ProjectDiscoveryService } from '../src/main/services/project-discovery.service';
import { WorkflowService } from '../src/main/services/workflow.service';
import { ServiceOrchestrator } from '../src/main/services/service-orchestrator.service';
import { HealthCheckService } from '../src/main/services/health-check.service';
import { BatchOperationsService } from '../src/main/services/batch-operations.service';
import { FileManagerService } from '../src/main/services/file-manager.service';
import { GitAdapter } from '../src/main/plugins/git/git-adapter';
import { ProcessLauncher } from '../src/main/services/process-launcher.service';
import { PortMonitor } from '../src/main/services/port-monitor.service';
import { TYPES, IProject, IWorkflowGroup } from '../src/shared/types';

class DevWorkbenchEnhancedWebServer {
    private container: Container;
    private projectService!: ProjectDiscoveryService;
    private workflowService!: WorkflowService;
    private orchestrator!: ServiceOrchestrator;
    private batchOps!: BatchOperationsService;
    private fileManager!: FileManagerService;
    private app!: express.Application;
    private server!: http.Server;
    private port: number = 3336;

    constructor() {
        this.container = new Container();
        this.setupContainer();
        this.initializeServices();
        this.setupExpress();
        this.setupRoutes();
    }

    private setupContainer() {
        // 注册所有服务
        this.container.bind(TYPES.EventBus).to(EventBus).inSingletonScope();
        this.container.bind(TYPES.Logger).to(SimpleLogger).inSingletonScope();
        this.container.bind(TYPES.StateRepository).to(SimpleStateRepository).inSingletonScope();
        this.container.bind(TYPES.VersionControlAdapter).to(GitAdapter).inSingletonScope();
        this.container.bind(TYPES.ProjectDiscoveryService).to(ProjectDiscoveryService).inSingletonScope();
        this.container.bind(TYPES.ProcessLauncher).to(ProcessLauncher).inSingletonScope();
        this.container.bind(TYPES.PortMonitor).to(PortMonitor).inSingletonScope();
        this.container.bind(TYPES.WorkflowService).to(WorkflowService).inSingletonScope();
        this.container.bind(TYPES.ServiceOrchestrator).to(ServiceOrchestrator).inSingletonScope();
        this.container.bind(TYPES.HealthCheckService).to(HealthCheckService).inSingletonScope();
        this.container.bind(TYPES.BatchOperationsService).to(BatchOperationsService).inSingletonScope();
        this.container.bind('FileManagerService').to(FileManagerService).inSingletonScope();
    }

    private initializeServices() {
        this.projectService = this.container.get<ProjectDiscoveryService>(TYPES.ProjectDiscoveryService);
        this.workflowService = this.container.get<WorkflowService>(TYPES.WorkflowService);
        this.orchestrator = this.container.get<ServiceOrchestrator>(TYPES.ServiceOrchestrator);
        this.batchOps = this.container.get<BatchOperationsService>(TYPES.BatchOperationsService);
        this.fileManager = this.container.get<FileManagerService>('FileManagerService');
    }

    private setupExpress() {
        this.app = express();
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.static('public'));

        // CORS支持
        this.app.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH');
            res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key');
            next();
        });

        // API Key中间件（为n8n集成准备）
        this.app.use('/api', (req, res, next) => {
            const apiKey = req.headers['x-api-key'];
            if (req.path.startsWith('/webhook') && !apiKey) {
                return res.status(401).json({ error: 'API Key required for webhook access' });
            }
            next();
        });
    }

    private setupRoutes() {
        // 主页
        this.app.get('/', (req, res) => {
            res.send(this.getEnhancedHTML());
        });

        // 项目管理API
        this.setupProjectRoutes();

        // 文件管理API
        this.setupFileRoutes();

        // 工作流管理API
        this.setupWorkflowRoutes();

        // 批量操作API
        this.setupBatchRoutes();

        // 监控API
        this.setupMonitoringRoutes();

        // n8n集成API
        this.setupN8nRoutes();
    }

    private setupProjectRoutes() {
        // 获取项目列表
        this.app.get('/api/projects', async (req, res) => {
            try {
                const projects = this.projectService.getProjects();
                res.json({ success: true, data: projects });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 扫描项目
        this.app.post('/api/projects/scan', async (req, res) => {
            try {
                const { path: scanPath = '.', recursive = true } = req.body;
                const projects = await this.projectService.scanDirectory(scanPath);
                res.json({ success: true, data: projects });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 获取项目详情
        this.app.get('/api/projects/:id', async (req, res) => {
            try {
                const { id } = req.params;
                const projects = this.projectService.getProjects();
                const project = projects.find(p => p.id === id);

                if (!project) {
                    return res.status(404).json({ success: false, error: '项目不存在' });
                }

                res.json({ success: true, data: project });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 刷新项目
        this.app.post('/api/projects/:id/refresh', async (req, res) => {
            try {
                const { id } = req.params;
                await this.projectService.refreshProject(id);
                res.json({ success: true, message: '项目刷新成功' });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });
    }

    private setupFileRoutes() {
        // 获取目录树
        this.app.get('/api/files/tree', async (req, res) => {
            try {
                const { path: dirPath = '.', depth = 3 } = req.query;
                const tree = await this.fileManager.getDirectoryTree(dirPath as string, Number(depth));
                res.json({ success: true, data: tree });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 获取文件内容
        this.app.get('/api/files/content', async (req, res) => {
            try {
                const { path: filePath } = req.query;
                if (!filePath) {
                    return res.status(400).json({ success: false, error: '文件路径不能为空' });
                }

                const content = await this.fileManager.getFileContent(filePath as string);
                res.json({ success: true, data: { content, path: filePath } });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 获取文件统计
        this.app.get('/api/files/stats', async (req, res) => {
            try {
                const { path: filePath } = req.query;
                if (!filePath) {
                    return res.status(400).json({ success: false, error: '文件路径不能为空' });
                }

                const stats = await this.fileManager.getFileStats(filePath as string);
                res.json({ success: true, data: stats });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 搜索文件
        this.app.get('/api/files/search', async (req, res) => {
            try {
                const { path: rootPath = '.', pattern } = req.query;
                if (!pattern) {
                    return res.status(400).json({ success: false, error: '搜索模式不能为空' });
                }

                const results = await this.fileManager.searchFiles(rootPath as string, pattern as string);
                res.json({ success: true, data: results });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });
    }

    private setupWorkflowRoutes() {
        // 获取工作流组列表
        this.app.get('/api/workflows', async (req, res) => {
            try {
                const workflows = await this.workflowService.getWorkflowGroups();
                res.json({ success: true, data: workflows });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 创建工作流组
        this.app.post('/api/workflows', async (req, res) => {
            try {
                const workflowData = req.body;
                const workflow = await this.workflowService.createWorkflowGroup(workflowData);
                res.json({ success: true, data: workflow });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 启动工作流
        this.app.post('/api/workflows/:id/start', async (req, res) => {
            try {
                const { id } = req.params;
                const workflow = await this.workflowService.getWorkflowGroup(id);
                if (!workflow) {
                    return res.status(404).json({ success: false, error: '工作流组不存在' });
                }

                const execution = await this.orchestrator.startWorkflowGroup(workflow);
                res.json({ success: true, data: execution });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 停止工作流
        this.app.post('/api/workflows/:id/stop', async (req, res) => {
            try {
                const { id } = req.params;
                await this.orchestrator.stopWorkflowGroup(id);
                res.json({ success: true, message: '工作流已停止' });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 获取工作流执行状态
        this.app.get('/api/workflows/executions', async (req, res) => {
            try {
                const executions = this.orchestrator.getActiveExecutions();
                res.json({ success: true, data: executions });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });
    }

    private setupBatchRoutes() {
        // 批量操作
        this.app.post('/api/batch/:operation', async (req, res) => {
            try {
                const { operation } = req.params;
                const { projectIds } = req.body;

                if (!projectIds || !Array.isArray(projectIds)) {
                    return res.status(400).json({ success: false, error: '项目ID列表不能为空' });
                }

                let result;
                switch (operation) {
                    case 'git-pull':
                        result = await this.batchOps.batchGitPull(projectIds);
                        break;
                    case 'git-status':
                        result = await this.batchOps.batchGitStatus(projectIds);
                        break;
                    case 'refresh':
                        result = await this.batchOps.batchRefreshProjects(projectIds);
                        break;
                    case 'open-vscode':
                        result = await this.batchOps.batchOpenInVSCode(projectIds);
                        break;
                    case 'open-terminal':
                        result = await this.batchOps.batchOpenInTerminal(projectIds);
                        break;
                    default:
                        return res.status(400).json({ success: false, error: '未知操作' });
                }

                res.json({ success: true, data: result });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });
    }

    private setupMonitoringRoutes() {
        // 系统状态
        this.app.get('/api/status', (req, res) => {
            const projects = this.projectService.getProjects();
            const executions = this.orchestrator.getActiveExecutions();

            res.json({
                success: true,
                data: {
                    projectCount: projects.length,
                    activeWorkflows: executions.length,
                    systemStatus: 'running',
                    uptime: process.uptime(),
                    memory: process.memoryUsage(),
                    version: '2.0.0'
                }
            });
        });

        // 系统状态 (前端兼容)
        this.app.get('/api/system/status', (req, res) => {
            const projects = this.projectService.getProjects();
            const executions = this.orchestrator.getActiveExecutions();
            const memUsage = process.memoryUsage();

            res.json({
                success: true,
                data: {
                    '项目数量': projects.length,
                    '活跃工作流': executions.length,
                    '系统状态': '运行中',
                    '运行时间': `${Math.floor(process.uptime() / 60)}分${Math.floor(process.uptime() % 60)}秒`,
                    '内存使用': `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
                    '版本': '2.0.0',
                    '端口': this.port
                }
            });
        });

        // 端口扫描
        this.app.post('/api/ports/scan', async (req, res) => {
            try {
                // 模拟端口扫描
                const commonPorts = [3000, 3001, 3333, 8080, 8081, 9000];
                const results = commonPorts.map(port => ({
                    port,
                    status: port === this.port ? '使用中' : '空闲'
                }));

                res.json({
                    success: true,
                    data: results
                });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // 实时事件流 (Server-Sent Events)
        this.app.get('/api/events/stream', (req, res) => {
            res.writeHead(200, {
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*',
            });

            // 发送初始连接事件
            res.write(`data: ${JSON.stringify({ type: 'connected', timestamp: new Date() })}\n\n`);

            // 设置心跳
            const heartbeat = setInterval(() => {
                res.write(`data: ${JSON.stringify({ type: 'heartbeat', timestamp: new Date() })}\n\n`);
            }, 30000);

            // 清理连接
            req.on('close', () => {
                clearInterval(heartbeat);
            });
        });
    }

    private setupN8nRoutes() {
        // n8n Webhook端点
        this.app.post('/api/webhook/:workflowId', async (req, res) => {
            try {
                const { workflowId } = req.params;
                const payload = req.body;

                // 处理n8n webhook请求
                const result = {
                    workflowId,
                    timestamp: new Date(),
                    payload,
                    status: 'received'
                };

                res.json({ success: true, data: result });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // n8n兼容的项目数据格式
        this.app.get('/api/n8n/projects', async (req, res) => {
            try {
                const projects = this.projectService.getProjects();
                const n8nFormat = projects.map(project => ({
                    id: project.id,
                    name: project.name,
                    type: project.type,
                    path: project.path,
                    git: project.gitInfo ? {
                        branch: project.gitInfo.branch,
                        hasChanges: project.gitInfo.hasUncommittedChanges,
                        remoteUrl: project.gitInfo.remoteUrl
                    } : null,
                    metadata: {
                        lastAccessed: project.lastAccessed?.toISOString() || null
                    }
                }));

                res.json({ success: true, data: n8nFormat });
            } catch (error) {
                res.status(500).json({ success: false, error: (error as Error).message });
            }
        });

        // API文档
        this.app.get('/api/docs', (req, res) => {
            res.json({
                title: 'DevWorkbench API Documentation',
                version: '2.0.0',
                description: 'Enhanced API for project management and n8n integration',
                endpoints: {
                    projects: {
                        'GET /api/projects': 'Get all projects',
                        'POST /api/projects/scan': 'Scan for projects',
                        'GET /api/projects/:id': 'Get project details',
                        'POST /api/projects/:id/refresh': 'Refresh project'
                    },
                    files: {
                        'GET /api/files/tree': 'Get directory tree',
                        'GET /api/files/content': 'Get file content',
                        'GET /api/files/search': 'Search files'
                    },
                    workflows: {
                        'GET /api/workflows': 'Get workflow groups',
                        'POST /api/workflows': 'Create workflow group',
                        'POST /api/workflows/:id/start': 'Start workflow',
                        'POST /api/workflows/:id/stop': 'Stop workflow'
                    },
                    batch: {
                        'POST /api/batch/:operation': 'Execute batch operation'
                    },
                    n8n: {
                        'GET /api/n8n/projects': 'Get projects in n8n format',
                        'POST /api/webhook/:workflowId': 'n8n webhook endpoint'
                    }
                }
            });
        });
    }

    private getEnhancedHTML(): string {
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevWorkbench Enhanced - 开发环境管理工具</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .nav-tabs {
            display: flex;
            background: rgba(255,255,255,0.1);
            border-radius: 12px;
            padding: 5px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        .nav-tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            background: transparent;
            border: none;
            color: white;
            cursor: pointer;
            border-radius: 8px;
            transition: all 0.3s;
            font-size: 14px;
        }
        .nav-tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            margin: 3px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.danger {
            background: #dc3545;
        }
        .file-tree {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            background: #f8f9fa;
        }
        .file-item {
            padding: 4px 8px;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .file-item:hover {
            background: #e9ecef;
        }
        .file-item.selected {
            background: #007bff;
            color: white;
        }
        .file-item.directory {
            font-weight: bold;
        }
        .project-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
        }
        .project-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s;
            cursor: pointer;
        }
        .project-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .project-card.selected {
            border-color: #007bff;
            background: #e3f2fd;
        }
        .project-card h4 {
            margin-bottom: 8px;
            color: #495057;
        }
        .project-card .meta {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 8px;
        }
        .project-card .actions {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.online { background: #28a745; }
        .status-indicator.offline { background: #dc3545; }
        .status-indicator.pending { background: #ffc107; }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
        .search-box {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin-bottom: 10px;
        }
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 20px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        .close {
            float: right;
            font-size: 24px;
            cursor: pointer;
            color: #aaa;
        }
        .close:hover {
            color: #000;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DevWorkbench Enhanced</h1>
            <p>开发环境管理工具 - 增强版Web界面</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('projects')">📦 项目管理</button>
            <button class="nav-tab" onclick="showTab('files')">📁 文件浏览</button>
            <button class="nav-tab" onclick="showTab('workflows')">🔄 工作流</button>
            <button class="nav-tab" onclick="showTab('batch')">📊 批量操作</button>
            <button class="nav-tab" onclick="showTab('monitor')">🔍 监控</button>
            <button class="nav-tab" onclick="showTab('api')">🔗 API</button>
        </div>

        <!-- 项目管理 -->
        <div id="projects" class="tab-content active">
            <div class="card">
                <h3>📦 项目管理</h3>
                <div class="dashboard">
                    <div class="card">
                        <h4>🔍 项目扫描</h4>
                        <button class="btn" onclick="scanProjects()">扫描项目</button>
                        <div id="project-list">点击扫描按钮开始...</div>
                    </div>
                    <div class="card">
                        <h4>📊 项目状态</h4>
                        <div id="project-status">等待扫描...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 文件浏览 -->
        <div id="files" class="tab-content">
            <div class="card">
                <h3>📁 文件浏览</h3>
                <div class="dashboard">
                    <div class="card">
                        <h4>📂 目录结构</h4>
                        <button class="btn" onclick="loadFileTree()">加载文件树</button>
                        <div id="file-tree">点击加载文件树...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工作流 -->
        <div id="workflows" class="tab-content">
            <div class="card">
                <h3>🔄 工作流管理</h3>
                <div class="dashboard">
                    <div class="card">
                        <h4>⚡ 快速操作</h4>
                        <button class="btn" onclick="runWorkflow('build')">构建项目</button>
                        <button class="btn" onclick="runWorkflow('test')">运行测试</button>
                        <button class="btn" onclick="runWorkflow('deploy')">部署</button>
                        <div id="workflow-output">选择操作...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批量操作 -->
        <div id="batch" class="tab-content">
            <div class="card">
                <h3>📊 批量操作</h3>
                <div class="dashboard">
                    <div class="card">
                        <h4>🔄 Git 操作</h4>
                        <button class="btn" onclick="batchGitStatus()">批量Git状态</button>
                        <button class="btn" onclick="batchGitPull()">批量Git拉取</button>
                        <div id="batch-output">选择批量操作...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 监控 -->
        <div id="monitor" class="tab-content">
            <div class="card">
                <h3>🔍 系统监控</h3>
                <div class="dashboard">
                    <div class="card">
                        <h4>📈 系统状态</h4>
                        <button class="btn" onclick="loadSystemStatus()">刷新状态</button>
                        <div id="system-status">点击刷新状态...</div>
                    </div>
                    <div class="card">
                        <h4>🔌 端口监控</h4>
                        <button class="btn" onclick="scanPorts()">扫描端口</button>
                        <div id="port-status">点击扫描端口...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API -->
        <div id="api" class="tab-content">
            <div class="card">
                <h3>🔗 API 测试</h3>
                <div class="dashboard">
                    <div class="card">
                        <h4>🧪 API 调用</h4>
                        <button class="btn" onclick="testApi('/api/projects/scan')">测试项目扫描</button>
                        <button class="btn" onclick="testApi('/api/system/status')">测试系统状态</button>
                        <button class="btn" onclick="testApi('/api/ports/scan')">测试端口扫描</button>
                        <div id="api-result">选择API测试...</div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        // 全局变量
        let currentTab = 'projects';

        // 标签页切换
        function showTab(tabId) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(el => {
                el.style.display = 'none';
                el.classList.remove('active');
            });

            // 移除所有标签按钮的激活状态
            document.querySelectorAll('.nav-tab').forEach(el => {
                el.classList.remove('active');
            });

            // 显示选中的标签页
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.style.display = 'block';
                targetTab.classList.add('active');
            }

            // 激活对应的标签按钮
            const activeButton = document.querySelector(\`button[onclick="showTab('\${tabId}')"]\`);
            if (activeButton) {
                activeButton.classList.add('active');
            }

            currentTab = tabId;
            console.log('切换到标签页:', tabId);
        }

        // API 调用封装
        async function apiCall(endpoint, options = {}) {
            try {
                console.log('API调用:', endpoint, options);
                const response = await fetch(endpoint, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(\`HTTP error! status: \${response.status}\`);
                }

                const result = await response.json();
                console.log('API响应:', result);
                return result;
            } catch (error) {
                console.error('API调用失败:', error);
                return { success: false, error: error.message };
            }
        }

        // 项目扫描
        async function scanProjects() {
            const listEl = document.getElementById('project-list');
            const statusEl = document.getElementById('project-status');

            listEl.innerHTML = '🔄 正在扫描项目...';
            statusEl.innerHTML = '扫描中...';

            try {
                const result = await apiCall('/api/projects/scan', {
                    method: 'POST',
                    body: JSON.stringify({ path: '.' })
                });

                if (result.success && result.data) {
                    let html = '<div class="project-grid">';
                    result.data.forEach(project => {
                        html += \`
                            <div class="project-item">
                                <h4>\${project.name}</h4>
                                <p>类型: \${project.type}</p>
                                <p>路径: \${project.path}</p>
                            </div>
                        \`;
                    });
                    html += '</div>';
                    listEl.innerHTML = html;
                    statusEl.innerHTML = \`找到 \${result.data.length} 个项目\`;
                } else {
                    listEl.innerHTML = '❌ 扫描失败: ' + (result.error || '未知错误');
                    statusEl.innerHTML = '扫描失败';
                }
            } catch (error) {
                listEl.innerHTML = '❌ 扫描出错: ' + error.message;
                statusEl.innerHTML = '扫描出错';
            }
        }

        // 加载文件树
        async function loadFileTree() {
            const treeEl = document.getElementById('file-tree');
            treeEl.innerHTML = '🔄 正在加载文件树...';

            try {
                const result = await apiCall('/api/files/tree');
                if (result.success && result.data) {
                    treeEl.innerHTML = '<pre>' + JSON.stringify(result.data, null, 2) + '</pre>';
                } else {
                    treeEl.innerHTML = '❌ 加载失败: ' + (result.error || '未知错误');
                }
            } catch (error) {
                treeEl.innerHTML = '❌ 加载出错: ' + error.message;
            }
        }

        // 运行工作流
        async function runWorkflow(type) {
            const outputEl = document.getElementById('workflow-output');
            outputEl.innerHTML = \`🔄 正在执行 \${type}...\`;

            try {
                const result = await apiCall(\`/api/workflows/\${type}\`, { method: 'POST' });
                if (result.success) {
                    outputEl.innerHTML = \`✅ \${type} 执行成功\`;
                } else {
                    outputEl.innerHTML = \`❌ \${type} 执行失败: \` + (result.error || '未知错误');
                }
            } catch (error) {
                outputEl.innerHTML = \`❌ \${type} 执行出错: \` + error.message;
            }
        }

        // 批量Git状态
        async function batchGitStatus() {
            const outputEl = document.getElementById('batch-output');
            outputEl.innerHTML = '🔄 正在检查Git状态...';

            try {
                const result = await apiCall('/api/batch/git-status', { method: 'POST' });
                if (result.success && result.data) {
                    let html = '<div class="git-status-list">';
                    result.data.forEach(item => {
                        html += \`<div>\${item.project}: \${item.status}</div>\`;
                    });
                    html += '</div>';
                    outputEl.innerHTML = html;
                } else {
                    outputEl.innerHTML = '❌ 检查失败: ' + (result.error || '未知错误');
                }
            } catch (error) {
                outputEl.innerHTML = '❌ 检查出错: ' + error.message;
            }
        }

        // 批量Git拉取
        async function batchGitPull() {
            const outputEl = document.getElementById('batch-output');
            outputEl.innerHTML = '🔄 正在执行Git拉取...';

            try {
                const result = await apiCall('/api/batch/git-pull', { method: 'POST' });
                if (result.success) {
                    outputEl.innerHTML = '✅ Git拉取完成';
                } else {
                    outputEl.innerHTML = '❌ Git拉取失败: ' + (result.error || '未知错误');
                }
            } catch (error) {
                outputEl.innerHTML = '❌ Git拉取出错: ' + error.message;
            }
        }

        // 加载系统状态
        async function loadSystemStatus() {
            const statusEl = document.getElementById('system-status');
            statusEl.innerHTML = '🔄 正在加载系统状态...';

            try {
                const result = await apiCall('/api/system/status');
                if (result.success && result.data) {
                    let html = '<div class="system-info">';
                    Object.entries(result.data).forEach(([key, value]) => {
                        html += \`<div><strong>\${key}:</strong> \${value}</div>\`;
                    });
                    html += '</div>';
                    statusEl.innerHTML = html;
                } else {
                    statusEl.innerHTML = '❌ 加载失败: ' + (result.error || '未知错误');
                }
            } catch (error) {
                statusEl.innerHTML = '❌ 加载出错: ' + error.message;
            }
        }

        // 扫描端口
        async function scanPorts() {
            const statusEl = document.getElementById('port-status');
            statusEl.innerHTML = '🔄 正在扫描端口...';

            try {
                const result = await apiCall('/api/ports/scan', { method: 'POST' });
                if (result.success && result.data) {
                    let html = '<div class="port-list">';
                    result.data.forEach(port => {
                        html += \`<div>端口 \${port.port}: \${port.status}</div>\`;
                    });
                    html += '</div>';
                    statusEl.innerHTML = html;
                } else {
                    statusEl.innerHTML = '❌ 扫描失败: ' + (result.error || '未知错误');
                }
            } catch (error) {
                statusEl.innerHTML = '❌ 扫描出错: ' + error.message;
            }
        }

        // API测试
        async function testApi(endpoint) {
            const resultEl = document.getElementById('api-result');
            resultEl.innerHTML = \`🔄 正在测试 \${endpoint}...\`;

            try {
                const result = await apiCall(endpoint, { method: 'POST' });
                resultEl.innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } catch (error) {
                resultEl.innerHTML = '❌ 测试出错: ' + error.message;
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DevWorkbench Enhanced 页面加载完成');
            showTab('projects');
        });
    </script>
</body>
</html>`;
    }

    /**
     * 启动服务器
     */
    async start(): Promise<void> {
        return new Promise((resolve, reject) => {
            this.server = this.app.listen(this.port, () => {
                console.log(`🚀 DevWorkbench Enhanced Web Server 已启动`);
                console.log(`📍 访问地址: http://localhost:${this.port}`);
                console.log(`📚 API文档: http://localhost:${this.port}/api/docs`);
                // 不要立即 resolve，让服务器保持运行
            });

            this.server.on('error', (error) => {
                console.error('服务器启动失败:', error);
                reject(error);
            });
        });
    }

    /**
     * 停止服务器
     */
    async stop(): Promise<void> {
        if (this.server) {
            return new Promise((resolve) => {
                this.server.close(() => {
                    console.log('🛑 服务器已停止');
                    resolve();
                });
            });
        }
    }
}

// 启动服务器
if (require.main === module) {
    const server = new DevWorkbenchEnhancedWebServer();
    server.start().catch(console.error);

    // 优雅关闭
    process.on('SIGINT', async () => {
        console.log('\n🛑 收到关闭信号，正在优雅关闭服务器...');
        await server.stop();
        process.exit(0);
    });

    process.on('SIGTERM', async () => {
        console.log('\n🛑 收到终止信号，正在优雅关闭服务器...');
        await server.stop();
        process.exit(0);
    });
}