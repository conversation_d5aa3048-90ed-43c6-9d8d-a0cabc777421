#!/usr/bin/env node
/**
 * DevWorkbench Web Server - Web界面版本
 * 提供基于浏览器的GUI界面
 */

import 'reflect-metadata';
import express from 'express';
import * as path from 'path';
import * as http from 'http';
import { Container } from 'inversify';
import { EventBus } from '../src/main/core/event-bus';
import { SimpleLogger } from '../src/main/services/logger-simple.service';
import { SimpleStateRepository } from '../src/main/services/state-simple.service';
import { ProjectDiscoveryService } from '../src/main/services/project-discovery.service';
import { WorkflowService } from '../src/main/services/workflow.service';
import { ServiceOrchestrator } from '../src/main/services/service-orchestrator.service';
import { HealthCheckService } from '../src/main/services/health-check.service';
import { BatchOperationsService } from '../src/main/services/batch-operations.service';
import { GitAdapter } from '../src/main/plugins/git/git-adapter';
import { ProcessLauncher } from '../src/main/services/process-launcher.service';
import { PortMonitor } from '../src/main/services/port-monitor.service';
import { TYPES, IProject, IWorkflowGroup } from '../src/shared/types';

class DevWorkbenchWebServer {
  private container: Container;
  private projectService!: ProjectDiscoveryService;
  private workflowService!: WorkflowService;
  private orchestrator!: ServiceOrchestrator;
  private batchOps!: BatchOperationsService;
  private app!: express.Application;
  private server!: http.Server;
  private port: number = 3333;

  constructor() {
    this.container = new Container();
    this.setupContainer();
    this.initializeServices();
    this.setupExpress();
    this.setupRoutes();
  }

  private setupContainer() {
    // 注册所有服务
    this.container.bind(TYPES.EventBus).to(EventBus).inSingletonScope();
    this.container.bind(TYPES.Logger).to(SimpleLogger).inSingletonScope();
    this.container.bind(TYPES.StateRepository).to(SimpleStateRepository).inSingletonScope();
    this.container.bind(TYPES.VersionControlAdapter).to(GitAdapter).inSingletonScope();
    this.container.bind(TYPES.ProjectDiscoveryService).to(ProjectDiscoveryService).inSingletonScope();
    this.container.bind(TYPES.ProcessLauncher).to(ProcessLauncher).inSingletonScope();
    this.container.bind(TYPES.PortMonitor).to(PortMonitor).inSingletonScope();
    this.container.bind(TYPES.WorkflowService).to(WorkflowService).inSingletonScope();
    this.container.bind(TYPES.ServiceOrchestrator).to(ServiceOrchestrator).inSingletonScope();
    this.container.bind(TYPES.HealthCheckService).to(HealthCheckService).inSingletonScope();
    this.container.bind(TYPES.BatchOperationsService).to(BatchOperationsService).inSingletonScope();
  }

  private initializeServices() {
    this.projectService = this.container.get<ProjectDiscoveryService>(TYPES.ProjectDiscoveryService);
    this.workflowService = this.container.get<WorkflowService>(TYPES.WorkflowService);
    this.orchestrator = this.container.get<ServiceOrchestrator>(TYPES.ServiceOrchestrator);
    this.batchOps = this.container.get<BatchOperationsService>(TYPES.BatchOperationsService);
  }

  private setupExpress() {
    this.app = express();
    this.app.use(express.json());
    this.app.use(express.static('public'));

    // CORS支持
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
      res.header('Access-Control-Allow-Headers', 'Content-Type');
      next();
    });
  }

  private setupRoutes() {
    // 主页
    this.app.get('/', (req, res) => {
      res.send(this.getMainHTML());
    });

    // API路由
    this.app.get('/api/projects', async (req, res) => {
      try {
        const projects = this.projectService.getProjects();
        res.json({ success: true, data: projects });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/projects/scan', async (req, res) => {
      try {
        const { path: scanPath = '.' } = req.body;
        const projects = await this.projectService.scanDirectory(scanPath);
        res.json({ success: true, data: projects });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.get('/api/workflows', async (req, res) => {
      try {
        const workflows = await this.workflowService.getWorkflowGroups();
        res.json({ success: true, data: workflows });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.post('/api/workflows/:id/start', async (req, res) => {
      try {
        const { id } = req.params;
        const workflow = await this.workflowService.getWorkflowGroup(id);
        if (!workflow) {
          return res.status(404).json({ success: false, error: '工作流组不存在' });
        }

        // 模拟启动
        res.json({ success: true, message: `工作流组 ${workflow.name} 启动成功 (模拟)` });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });

    this.app.get('/api/status', (req, res) => {
      const projects = this.projectService.getProjects();
      const executions = this.orchestrator.getActiveExecutions();

      res.json({
        success: true,
        data: {
          projectCount: projects.length,
          activeWorkflows: executions.length,
          systemStatus: 'running'
        }
      });
    });

    // 批量操作API
    this.app.post('/api/batch/:operation', async (req, res) => {
      try {
        const { operation } = req.params;
        const projects = this.projectService.getProjects();
        const projectIds = projects.map(p => p.id);

        if (projectIds.length === 0) {
          return res.status(400).json({ success: false, error: '没有项目' });
        }

        let result;
        switch (operation) {
          case 'git-pull':
            result = await this.batchOps.batchGitPull(projectIds);
            break;
          case 'git-status':
            result = await this.batchOps.batchGitStatus(projectIds);
            break;
          default:
            return res.status(400).json({ success: false, error: '未知操作' });
        }

        res.json({ success: true, data: result });
      } catch (error) {
        res.status(500).json({ success: false, error: (error as Error).message });
      }
    });
  }

  private getMainHTML(): string {
    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevWorkbench - 开发环境管理工具</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .status-badge {
            background: #4CAF50;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
        }
        .project-list, .workflow-list {
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .project-item, .workflow-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .project-item:last-child, .workflow-item:last-child {
            border-bottom: none;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DevWorkbench</h1>
            <p>开发环境管理工具 - Web界面</p>
        </div>

        <div class="dashboard">
            <div class="card">
                <h3>📦 项目管理</h3>
                <div class="status">
                    <span>项目数量: <span id="project-count">-</span></span>
                    <span class="status-badge">运行中</span>
                </div>
                <button class="btn" onclick="scanProjects()">🔍 扫描项目</button>
                <button class="btn" onclick="refreshProjects()">🔄 刷新列表</button>
                <div id="project-list" class="project-list">
                    <div class="loading">点击扫描项目开始...</div>
                </div>
            </div>

            <div class="card">
                <h3>🔄 工作流管理</h3>
                <div class="status">
                    <span>活动工作流: <span id="workflow-count">-</span></span>
                    <span class="status-badge">就绪</span>
                </div>
                <button class="btn" onclick="loadWorkflows()">📋 加载工作流</button>
                <button class="btn" onclick="createWorkflow()">➕ 创建工作流</button>
                <div id="workflow-list" class="workflow-list">
                    <div class="loading">点击加载工作流...</div>
                </div>
            </div>

            <div class="card">
                <h3>📊 批量操作</h3>
                <div class="status">
                    <span>可用操作: 5</span>
                    <span class="status-badge">就绪</span>
                </div>
                <button class="btn" onclick="batchGitPull()">⬇️ 批量Git Pull</button>
                <button class="btn" onclick="batchGitStatus()">📊 批量Git Status</button>
                <button class="btn" onclick="batchOpenVSCode()">🚀 批量打开VS Code</button>
                <div id="batch-result" style="margin-top: 10px; font-size: 14px; color: #666;">
                    选择批量操作...
                </div>
            </div>

            <div class="card">
                <h3>🔍 系统监控</h3>
                <div class="status">
                    <span>系统状态: <span id="system-status">-</span></span>
                    <span class="status-badge">正常</span>
                </div>
                <button class="btn" onclick="checkStatus()">🔄 刷新状态</button>
                <button class="btn" onclick="viewLogs()">📝 查看日志</button>
                <div style="margin-top: 10px; font-size: 14px;">
                    <div>内存使用: < 100MB</div>
                    <div>响应时间: < 10ms</div>
                    <div>运行时间: <span id="uptime">-</span></div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>DevWorkbench v2.0 - 让开发更高效，让工作更愉快！</p>
            <p>访问地址: <a href="http://localhost:${this.port}" style="color: #fff;">http://localhost:${this.port}</a></p>
        </div>
    </div>

    <script>
        let startTime = Date.now();

        // 更新运行时间
        function updateUptime() {
            const uptime = Math.floor((Date.now() - startTime) / 1000);
            document.getElementById('uptime').textContent = uptime + '秒';
        }
        setInterval(updateUptime, 1000);

        // API调用函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                });
                return await response.json();
            } catch (error) {
                console.error('API调用失败:', error);
                return { success: false, error: error.message };
            }
        }

        // 项目管理功能
        async function scanProjects() {
            document.getElementById('project-list').innerHTML = '<div class="loading">扫描中...</div>';
            const result = await apiCall('/api/projects/scan', { method: 'POST' });
            
            if (result.success) {
                displayProjects(result.data);
                document.getElementById('project-count').textContent = result.data.length;
            } else {
                document.getElementById('project-list').innerHTML = '<div style="color: red;">扫描失败: ' + result.error + '</div>';
            }
        }

        async function refreshProjects() {
            const result = await apiCall('/api/projects');
            if (result.success) {
                displayProjects(result.data);
                document.getElementById('project-count').textContent = result.data.length;
            }
        }

        function displayProjects(projects) {
            const container = document.getElementById('project-list');
            if (projects.length === 0) {
                container.innerHTML = '<div class="loading">没有发现项目</div>';
                return;
            }

            container.innerHTML = projects.map(project => 
                '<div class="project-item">' +
                '<div>' +
                '<strong>' + project.name + '</strong><br>' +
                '<small>' + project.type + ' - ' + project.path + '</small>' +
                '</div>' +
                '<button class="btn" onclick="openProject(\\''+project.id+'\\')">打开</button>' +
                '</div>'
            ).join('');
        }

        function openProject(projectId) {
            alert('打开项目: ' + projectId + ' (模拟)');
        }

        // 工作流管理功能
        async function loadWorkflows() {
            document.getElementById('workflow-list').innerHTML = '<div class="loading">加载中...</div>';
            const result = await apiCall('/api/workflows');
            
            if (result.success) {
                displayWorkflows(result.data);
                document.getElementById('workflow-count').textContent = result.data.length;
            } else {
                document.getElementById('workflow-list').innerHTML = '<div style="color: red;">加载失败: ' + result.error + '</div>';
            }
        }

        function displayWorkflows(workflows) {
            const container = document.getElementById('workflow-list');
            if (workflows.length === 0) {
                container.innerHTML = '<div class="loading">没有工作流组</div>';
                return;
            }

            container.innerHTML = workflows.map(workflow => 
                '<div class="workflow-item">' +
                '<div>' +
                '<strong>' + workflow.name + '</strong><br>' +
                '<small>' + workflow.services.length + ' 个服务</small>' +
                '</div>' +
                '<button class="btn" onclick="startWorkflow(\\''+workflow.id+'\\')">启动</button>' +
                '</div>'
            ).join('');
        }

        async function startWorkflow(workflowId) {
            const result = await apiCall('/api/workflows/' + workflowId + '/start', { method: 'POST' });
            alert(result.success ? result.message : '启动失败: ' + result.error);
        }

        function createWorkflow() {
            alert('创建工作流功能 (演示模式)');
        }

        // 批量操作功能
        async function batchGitPull() {
            document.getElementById('batch-result').innerHTML = '执行中...';
            const result = await apiCall('/api/batch/git-pull', { method: 'POST' });
            
            if (result.success) {
                document.getElementById('batch-result').innerHTML = 
                    '✅ Git Pull 完成: 成功 ' + result.data.successCount + ', 失败 ' + result.data.failureCount;
            } else {
                document.getElementById('batch-result').innerHTML = '❌ 操作失败: ' + result.error;
            }
        }

        async function batchGitStatus() {
            document.getElementById('batch-result').innerHTML = '检查中...';
            const result = await apiCall('/api/batch/git-status', { method: 'POST' });
            
            if (result.success) {
                document.getElementById('batch-result').innerHTML = 
                    '✅ Git Status 完成: 成功 ' + result.data.successCount + ', 失败 ' + result.data.failureCount;
            } else {
                document.getElementById('batch-result').innerHTML = '❌ 操作失败: ' + result.error;
            }
        }

        function batchOpenVSCode() {
            document.getElementById('batch-result').innerHTML = '✅ 批量打开VS Code完成 (模拟)';
        }

        // 系统监控功能
        async function checkStatus() {
            const result = await apiCall('/api/status');
            if (result.success) {
                document.getElementById('system-status').textContent = result.data.systemStatus;
                document.getElementById('project-count').textContent = result.data.projectCount;
                document.getElementById('workflow-count').textContent = result.data.activeWorkflows;
            }
        }

        function viewLogs() {
            alert('查看日志功能 (演示模式)\\n\\n日志文件: test.log\\n系统状态: 正常\\n最近活动: 项目扫描、工作流管理');
        }

        // 初始化
        checkStatus();
        updateUptime();
    </script>
</body>
</html>
    `;
  }

  async start() {
    // 检查端口是否可用
    const portMonitor = this.container.get<PortMonitor>(TYPES.PortMonitor);
    const isPortAvailable = await portMonitor.checkPort(this.port);

    if (!isPortAvailable) {
      console.log(`⚠️  端口 ${this.port} 被占用，尝试使用端口 ${this.port + 1}`);
      this.port = this.port + 1;
    }

    this.server = this.app.listen(this.port, () => {
      console.log(`
🚀 DevWorkbench Web Server 启动成功！

📍 访问地址: http://localhost:${this.port}
🔧 API端点: http://localhost:${this.port}/api
📊 功能: 项目管理、工作流管理、批量操作、系统监控

按 Ctrl+C 停止服务器
`);
    });

    // 优雅关闭
    process.on('SIGINT', () => {
      console.log('\\n🛑 正在关闭服务器...');
      this.server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
      });
    });
  }
}

// 运行Web服务器
if (require.main === module) {
  const webServer = new DevWorkbenchWebServer();
  webServer.start().catch(console.error);
}

export { DevWorkbenchWebServer };
