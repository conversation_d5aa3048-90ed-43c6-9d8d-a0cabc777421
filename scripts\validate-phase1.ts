import "reflect-metadata";
import { Container } from "inversify";
import { TYPES, ILogger, IEventBus, ISecurityVault } from "../src/shared/types";
import { EventBus } from "../src/main/core/event-bus";
import { Logger } from "../src/main/services/Logger";
import { SecurityVault } from "../src/main/services/SecurityVault";

async function validate() {
    console.log("Running Phase 1 Validation Script...");
    try {
        // 创建一个简化的容器，不包含需要 Electron 的服务
        const container = new Container();
        container.bind<IEventBus>(TYPES.EventBus).to(EventBus).inSingletonScope();
        container.bind<ILogger>(TYPES.Logger).to(Logger).inSingletonScope();
        container.bind<ISecurityVault>(TYPES.SecurityVault).to(SecurityVault).inSingletonScope();

        const logger = container.get<ILogger>(TYPES.Logger);
        const eventBus = container.get<IEventBus>(TYPES.EventBus);
        const vault = container.get<ISecurityVault>(TYPES.SecurityVault);

        logger.info("Successfully resolved Logger.");
        logger.info("Successfully resolved EventBus.");
        logger.info("Successfully resolved SecurityVault.");

        eventBus.emitEvent("validation.test", { success: true });

        logger.info("All core services resolved and are functional.");
        console.log("Validation PASSED!");
    } catch (error) {
        console.error("Validation FAILED!", error);
        process.exit(1);
    }
}

validate();
