{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020"], "moduleResolution": "node", "outDir": "../dist/scripts", "noEmit": false, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowJs": true, "baseUrl": ".", "paths": {"@/*": ["../src/*"], "@main/*": ["../src/main/*"], "@renderer/*": ["../src/renderer/*"], "@shared/*": ["../src/shared/*"]}}, "include": ["./*.ts", "../src/shared/**/*"], "exclude": ["../node_modules", "../dist"]}