/**
 * 批量操作服务
 * 处理多项目的批量操作和统一管理
 */

import { injectable, inject } from 'inversify';
import { spawn, ChildProcess } from 'child_process';
import {
  IProject,
  ILogger,
  IVersionControlAdapter,
  IProjectDiscoveryService,
  TYPES,
  CoreEvents
} from '../../shared/types';
import { EventBus } from '../core/event-bus';

export interface IBatchOperationsService {
  batchGitPull(projectIds: string[]): Promise<BatchOperationResult>;
  batchGitStatus(projectIds: string[]): Promise<BatchOperationResult>;
  batchRefreshProjects(projectIds: string[]): Promise<BatchOperationResult>;
  batchOpenInVSCode(projectIds: string[]): Promise<BatchOperationResult>;
  batchOpenInTerminal(projectIds: string[]): Promise<BatchOperationResult>;
  batchOpenInExplorer(projectIds: string[]): Promise<BatchOperationResult>;
  executeCustomBatchCommand(projectIds: string[], command: string): Promise<BatchOperationResult>;
}

export interface BatchOperationResult {
  operationId: string;
  operationType: string;
  totalCount: number;
  successCount: number;
  failureCount: number;
  results: OperationResult[];
  startedAt: Date;
  completedAt: Date;
  duration: number;
}

export interface OperationResult {
  projectId: string;
  projectName: string;
  success: boolean;
  error?: string;
  data?: any;
  duration: number;
}

@injectable()
export class BatchOperationsService implements IBatchOperationsService {
  constructor(
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.VersionControlAdapter) private gitAdapter: IVersionControlAdapter,
    @inject(TYPES.ProjectDiscoveryService) private projectService: IProjectDiscoveryService,
    @inject(TYPES.EventBus) private eventBus: EventBus
  ) { }

  /**
   * 批量执行Git Pull
   */
  async batchGitPull(projectIds: string[]): Promise<BatchOperationResult> {
    return this.executeBatchOperation(
      'git_pull',
      projectIds,
      async (project: IProject) => {
        await this.gitAdapter.pull(project.path);
        return { message: 'Git pull 成功' };
      }
    );
  }

  /**
   * 批量获取Git状态
   */
  async batchGitStatus(projectIds: string[]): Promise<BatchOperationResult> {
    return this.executeBatchOperation(
      'git_status',
      projectIds,
      async (project: IProject) => {
        const status = await this.gitAdapter.getStatus(project.path);
        return status;
      }
    );
  }

  /**
   * 批量刷新项目
   */
  async batchRefreshProjects(projectIds: string[]): Promise<BatchOperationResult> {
    return this.executeBatchOperation(
      'refresh_projects',
      projectIds,
      async (project: IProject) => {
        await this.projectService.refreshProject(project.id);
        return { message: '项目刷新成功' };
      }
    );
  }

  /**
   * 批量在VS Code中打开
   */
  async batchOpenInVSCode(projectIds: string[]): Promise<BatchOperationResult> {
    return this.executeBatchOperation(
      'open_vscode',
      projectIds,
      async (project: IProject) => {
        const { spawn } = require('child_process');
        spawn('code', [project.path], { detached: true });
        return { message: '已在VS Code中打开' };
      }
    );
  }

  /**
   * 批量在终端中打开
   */
  async batchOpenInTerminal(projectIds: string[]): Promise<BatchOperationResult> {
    return this.executeBatchOperation(
      'open_terminal',
      projectIds,
      async (project: IProject) => {
        const { spawn } = require('child_process');
        if (process.platform === 'win32') {
          spawn('powershell', ['-Command', `cd "${project.path}"; powershell`], { detached: true });
        } else {
          spawn('open', ['-a', 'Terminal', project.path], { detached: true });
        }
        return { message: '已在终端中打开' };
      }
    );
  }

  /**
   * 批量在文件管理器中打开
   */
  async batchOpenInExplorer(projectIds: string[]): Promise<BatchOperationResult> {
    return this.executeBatchOperation(
      'open_explorer',
      projectIds,
      async (project: IProject) => {
        const { shell } = require('electron');
        await shell.openPath(project.path);
        return { message: '已在文件管理器中打开' };
      }
    );
  }

  /**
   * 执行自定义批量命令
   */
  async executeCustomBatchCommand(projectIds: string[], command: string): Promise<BatchOperationResult> {
    return this.executeBatchOperation(
      'custom_command',
      projectIds,
      async (project: IProject) => {
        return new Promise((resolve, reject) => {
          const child = spawn(command, [], {
            cwd: project.path,
            shell: true,
            stdio: 'pipe',
          });

          let stdout = '';
          let stderr = '';

          child.stdout?.on('data', (data) => {
            stdout += data.toString();
          });

          child.stderr?.on('data', (data) => {
            stderr += data.toString();
          });

          child.on('close', (code) => {
            if (code === 0) {
              resolve({
                message: '命令执行成功',
                stdout: stdout.trim(),
                stderr: stderr.trim(),
              });
            } else {
              reject(new Error(`命令执行失败 (退出码: ${code})\n${stderr}`));
            }
          });

          child.on('error', (error) => {
            reject(error);
          });

          // 设置超时
          setTimeout(() => {
            child.kill();
            reject(new Error('命令执行超时'));
          }, 30000); // 30秒超时
        });
      }
    );
  }

  /**
   * 执行批量操作的通用方法
   */
  private async executeBatchOperation(
    operationType: string,
    projectIds: string[],
    operation: (project: IProject) => Promise<any>
  ): Promise<BatchOperationResult> {
    const operationId = `batch_${operationType}_${Date.now()}`;
    const startedAt = new Date();

    this.logger.info(`开始批量操作: ${operationType}, 项目数量: ${projectIds.length}`);

    // 获取项目信息
    const projects = this.projectService.getProjects().filter(p => projectIds.includes(p.id));
    const results: OperationResult[] = [];

    // 发射批量操作开始事件
    this.eventBus.emitEvent(CoreEvents.PROCESS_STARTED, {
      type: 'batch_operation_started',
      operationId,
      operationType,
      projectCount: projects.length,
    });

    // 并发执行操作（限制并发数）
    const concurrencyLimit = 5;
    const chunks = this.chunkArray(projects, concurrencyLimit);

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (project) => {
        const operationStartTime = Date.now();

        try {
          const data = await operation(project);
          const duration = Date.now() - operationStartTime;

          const result: OperationResult = {
            projectId: project.id,
            projectName: project.name,
            success: true,
            data,
            duration,
          };

          results.push(result);

          // 发射单个操作成功事件
          this.eventBus.emitEvent(CoreEvents.PROCESS_OUTPUT, {
            type: 'batch_operation_item_success',
            operationId,
            projectId: project.id,
            projectName: project.name,
            data,
          });

        } catch (error) {
          const duration = Date.now() - operationStartTime;

          const result: OperationResult = {
            projectId: project.id,
            projectName: project.name,
            success: false,
            error: (error as Error).message,
            duration,
          };

          results.push(result);

          // 发射单个操作失败事件
          this.eventBus.emitEvent(CoreEvents.PROCESS_ERROR, {
            type: 'batch_operation_item_error',
            operationId,
            projectId: project.id,
            projectName: project.name,
            error: (error as Error).message,
          });

          this.logger.error(`批量操作失败: ${project.name}`, error as Error);
        }
      });

      // 等待当前批次完成
      await Promise.allSettled(chunkPromises);
    }

    const completedAt = new Date();
    const duration = completedAt.getTime() - startedAt.getTime();
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    const batchResult: BatchOperationResult = {
      operationId,
      operationType,
      totalCount: projects.length,
      successCount,
      failureCount,
      results,
      startedAt,
      completedAt,
      duration,
    };

    // 发射批量操作完成事件
    this.eventBus.emitEvent(CoreEvents.PROCESS_STOPPED, {
      type: 'batch_operation_completed',
      operationId,
      operationType,
      result: batchResult,
    });

    this.logger.info(`批量操作完成: ${operationType}, 成功: ${successCount}, 失败: ${failureCount}, 耗时: ${duration}ms`);

    return batchResult;
  }

  /**
   * 将数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 获取批量操作统计信息
   */
  getBatchOperationStats(): {
    totalOperations: number;
    successfulOperations: number;
    failedOperations: number;
    averageDuration: number;
  } {
    // 这里可以实现统计逻辑
    // 为简化，返回模拟数据
    return {
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      averageDuration: 0,
    };
  }
}
