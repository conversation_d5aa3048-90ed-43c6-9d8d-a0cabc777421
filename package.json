{"name": "dev-workbench", "version": "1.0.0", "description": "一个个人本地开发工作台", "main": "dist/main/index.js", "scripts": {"dev": "vite", "build": "npm run build:renderer && npm run build:main", "build:main": "tsc -p tsconfig.main.json", "build:renderer": "vite build", "build:scripts": "tsc -p scripts/tsconfig.json", "start": "electron .", "web": "npm run build:scripts && node dist/scripts/scripts/web-server-enhanced.js", "cli": "npm run build:scripts && node dist/scripts/scripts/cli.js", "interactive": "npm run build:scripts && node dist/scripts/scripts/interactive.js", "demo": "npm run build:scripts && node dist/scripts/scripts/demo.js", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "jest", "test-all": "npm run build:scripts && node dist/scripts/scripts/test-core.js && node dist/scripts/scripts/test-project-discovery.js && node dist/scripts/scripts/test-process-management.js && node dist/scripts/scripts/test-port-monitoring.js && node dist/scripts/scripts/test-workflow-management.js && node dist/scripts/scripts/integration-test.js", "package": "npm run build && electron-builder"}, "dependencies": {"antd": "^5.19.1", "electron-store": "^8.1.0", "eventemitter2": "^6.4.9", "express": "^4.19.2", "inversify": "^6.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "reflect-metadata": "^0.2.2", "simple-git": "^3.25.0", "winston": "^3.13.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0", "zustand": "^4.5.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "^20.14.9", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "@vitejs/plugin-react": "^4.3.1", "electron": "^31.0.2", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "jest": "^29.7.0", "ts-jest": "^29.4.0", "typescript": "^5.5.2", "vite": "^5.3.1"}}