/**
 * 共享类型定义
 * 定义了应用中使用的核心接口和类型
 */

// ============= 事件系统 =============
export interface DomainEvent {
  type: string;
  timestamp: number;
  payload: any;
}

export interface IEventBus {
  emit(event: DomainEvent): void;
  on(eventType: string, handler: (event: DomainEvent) => void): void;
  off(eventType: string, handler: (event: DomainEvent) => void): void;
  emitEvent(type: string, payload: any): void;
}

export enum CoreEvents {
  PROJECT_DISCOVERED = "project.discovered",
  PROJECT_UPDATED = "project.updated",
  PROCESS_STARTED = "process.started",
  PROCESS_STOPPED = "process.stopped",
  PROCESS_OUTPUT = "process.output",
  PROCESS_ERROR = "process.error",
  PROCESS_CRASHED = "process.crashed",
  PORT_AVAILABLE = "port.available",
  PORT_UNAVAILABLE = "port.unavailable",
  CONFIG_CHANGED = "config.changed",
}

// ============= 项目管理 =============
export enum ProjectType {
  NODE_JS = "nodejs",
  PYTHON = "python",
  JAVA = "java",
  DOTNET = "dotnet",
  GO = "go",
  RUST = "rust",
  UNKNOWN = "unknown",
}

export interface IGitInfo {
  branch: string;
  ahead: number;
  behind: number;
  hasUncommittedChanges: boolean;
  hasUntrackedFiles: boolean;
  remoteUrl?: string;
}

export interface IProject {
  id: string;
  name: string;
  path: string;
  type: ProjectType;
  gitInfo?: IGitInfo;
  lastAccessed?: Date;
  description?: string;
  detectedScripts?: { name: string; path: string; type: "ps1" | "bat" }[];
}

// ============= 工作流与进程管理 =============
export interface IWorkflowGroup {
  id: string;
  name: string;
  description?: string;
  services: IServiceConfig[];
  createdAt: Date;
  updatedAt: Date;
}

export interface IServiceConfig {
  id: string;
  projectId: string;
  name: string;
  command: string;
  env?: Record<string, string>;
  port?: number;
  healthCheckUrl?: string;
  dependsOn?: string[];
  autoRestart?: boolean;
  restartDelay?: number;
  orderIndex: number;
  source: "command" | "script";
}

export enum ProcessStatus {
  STARTING = "starting",
  RUNNING = "running",
  STOPPING = "stopping",
  STOPPED = "stopped",
  CRASHED = "crashed",
}

// ============= 核心服务接口 =============
export interface ILogger {
  debug(message: string, meta?: any): void;
  info(message: string, meta?: any): void;
  warn(message: string, meta?: any): void;
  error(message: string, error?: Error, meta?: any): void;
}

export interface IPersistenceService {
  get<T>(key: string): Promise<T | undefined>;
  set<T>(key: string, value: T): Promise<void>;
  delete(key: string): Promise<void>;
}

export interface ISecurityVault {
  store(key: string, value: string): Promise<void>;
  retrieve(key: string): Promise<string | null>;
  remove(key: string): Promise<void>;
  isEncryptionAvailable(): boolean;
}

export interface IVersionControlAdapter {
  isRepository(path: string): Promise<boolean>;
  getStatus(path: string): Promise<IGitInfo>;
  pull(path: string): Promise<void>;
}

export interface IProjectDiscoveryService {
  scanDirectory(scanPath: string): Promise<IProject[]>;
  getProjects(): Promise<IProject[]>;
  refreshProject(projectId: string): Promise<void>;
}

// ============= 进程管理 =============
export interface IProcessLauncher {
  start(config: ProcessConfig): Promise<ProcessHandle>;
  stop(handle: ProcessHandle): Promise<void>;
  stopAll(): Promise<void>;
  getRunningProcesses(): ProcessHandle[];
}

export interface ProcessConfig {
  id: string;
  name: string;
  command: string;
  args?: string[];
  cwd: string;
  env?: Record<string, string>;
  shell?: boolean;
}

export interface ProcessHandle {
  id: string;
  pid: number;
  config: ProcessConfig;
  status: ProcessStatus;
  outputStream: NodeJS.ReadableStream;
  errorStream: NodeJS.ReadableStream;
}

// ============= 端口监控 =============
export interface IPortMonitor {
  checkPort(port: number): Promise<boolean>;
  watch(port: number, callback: (isUsed: boolean) => void): IWatcher;
}

export interface IWatcher {
  stop(): void;
}

// ============= 依赖注入 =============
export const TYPES = {
  EventBus: Symbol.for("EventBus"),
  Logger: Symbol.for("Logger"),
  PersistenceService: Symbol.for("PersistenceService"),
  SecurityVault: Symbol.for("SecurityVault"),
  ProjectDiscoveryService: Symbol.for("ProjectDiscoveryService"),
  VersionControlAdapter: Symbol.for("VersionControlAdapter"),
  ProcessLauncher: Symbol.for("ProcessLauncher"),
  PortMonitor: Symbol.for("PortMonitor"),
  WorkflowService: Symbol.for("WorkflowService"),
  ServiceOrchestrator: Symbol.for("ServiceOrchestrator"),
  BatchOperationsService: Symbol.for("BatchOperationsService"),
} as const;