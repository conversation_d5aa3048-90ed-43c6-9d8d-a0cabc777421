/**
 * 事件总线实现
 * 基于 EventEmitter2 实现的事件驱动架构核心
 */

import { EventEmitter2 } from "eventemitter2";
import { injectable } from "inversify";
import { IEventBus, DomainEvent } from "../../shared/types";

@injectable()
export class EventBus implements IEventBus {
  private emitter: EventEmitter2;

  constructor() {
    this.emitter = new EventEmitter2({
      wildcard: true,
      delimiter: '.',
      newListener: false,
      maxListeners: 20,
      verboseMemoryLeak: true,
    });
  }

  /**
   * 发射事件
   */
  emit(event: DomainEvent): void {
    this.emitter.emit(event.type, event);
  }

  /**
   * 监听事件
   */
  on(eventType: string, handler: (event: DomainEvent) => void): void {
    this.emitter.on(eventType, handler);
  }

  /**
   * 取消监听事件
   */
  off(eventType: string, handler: (event: DomainEvent) => void): void {
    this.emitter.off(eventType, handler);
  }

  /**
   * 一次性监听事件
   */
  once(eventType: string, handler: (event: DomainEvent) => void): void {
    this.emitter.once(eventType, handler);
  }

  /**
   * 获取事件监听器数量
   */
  listenerCount(eventType: string): number {
    return this.emitter.listenerCount(eventType);
  }

  /**
   * 移除所有监听器
   */
  removeAllListeners(eventType?: string): void {
    this.emitter.removeAllListeners(eventType);
  }

  /**
   * 获取所有事件名称
   */
  eventNames(): string[] {
    return this.emitter.eventNames() as string[];
  }

  /**
   * 创建领域事件
   */
  createEvent(type: string, payload: any): DomainEvent {
    return {
      type,
      timestamp: Date.now(),
      payload,
    };
  }

  /**
   * 发射领域事件（便捷方法）
   */
  emitEvent(type: string, payload: any): void {
    const event = this.createEvent(type, payload);
    this.emit(event);
  }
}
