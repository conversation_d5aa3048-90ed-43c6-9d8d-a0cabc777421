/**
 * 主应用组件
 */

import React, { useEffect, useState } from 'react';
import './styles/App.css';

interface Project {
  name: string;
  path: string;
  status: string;
  gitStatus?: string;
  lastModified?: string;
}

interface SystemStatus {
  server: string;
  react: string;
  build: string;
  memory: string;
  uptime: string;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

const App: React.FC = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [systemStatus, setSystemStatus] = useState<SystemStatus>({
    server: '正常运行',
    react: '已加载',
    build: 'Vite + TypeScript',
    memory: '计算中...',
    uptime: '计算中...'
  });
  const [loading, setLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [activeTab, setActiveTab] = useState<'dashboard' | 'projects' | 'files' | 'api'>('dashboard');
  const [apiResult, setApiResult] = useState<string>('');

  // 获取真实的项目数据
  const fetchProjects = async () => {
    try {
      const response = await fetch('/api/projects');
      if (response.ok) {
        const data: ApiResponse<Project[]> = await response.json();
        if (data.success && data.data) {
          setProjects(data.data);
        }
      } else {
        // 如果 API 不可用，使用模拟数据
        setProjects([
          {
            name: 'DevWorkbench',
            path: process.cwd() || '/current/project',
            status: '运行中',
            gitStatus: 'Clean',
            lastModified: new Date().toLocaleString()
          },
          {
            name: 'Example Project',
            path: '/example/path',
            status: '就绪',
            gitStatus: 'Modified',
            lastModified: '2024-01-15 10:30:00'
          }
        ]);
      }
    } catch (error) {
      console.error('获取项目数据失败:', error);
      setProjects([
        {
          name: 'DevWorkbench',
          path: 'E:\\program\\GIT\\projectmanager',
          status: '运行中',
          gitStatus: 'Clean',
          lastModified: new Date().toLocaleString()
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  // 获取系统状态
  const fetchSystemStatus = async () => {
    try {
      const response = await fetch('/api/status');
      if (response.ok) {
        const data = await response.json();
        setSystemStatus(prev => ({
          ...prev,
          memory: data.memory || prev.memory,
          uptime: data.uptime || prev.uptime
        }));
      }
    } catch (error) {
      console.error('获取系统状态失败:', error);
    }
  };

  useEffect(() => {
    // 初始化应用
    console.log('DevWorkbench 应用已启动');

    // 更新时间
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // 加载数据
    fetchProjects();
    fetchSystemStatus();

    // 定期更新系统状态
    const statusTimer = setInterval(fetchSystemStatus, 30000);

    return () => {
      clearInterval(timer);
      clearInterval(statusTimer);
    };
  }, []);

  // 功能函数
  const handleRefreshProjects = () => {
    setLoading(true);
    fetchProjects();
  };

  const handleApiTest = async () => {
    try {
      setApiResult('测试中...');
      const response = await fetch('/api/status');
      const data = await response.json();
      setApiResult(JSON.stringify(data, null, 2));
    } catch (error) {
      setApiResult(`错误: ${error}`);
    }
  };

  const handleProjectAction = async (projectName: string, action: string) => {
    try {
      const response = await fetch(`/api/projects/${encodeURIComponent(projectName)}/${action}`, {
        method: 'POST'
      });
      const result = await response.json();
      setApiResult(`${action} 结果: ${JSON.stringify(result, null, 2)}`);
      // 刷新项目列表
      fetchProjects();
    } catch (error) {
      setApiResult(`操作失败: ${error}`);
    }
  };

  return (
    <div className="app-layout">
      <header className="app-header">
        <div className="app-title">
          <h1>🚀 DevWorkbench</h1>
          <span className="app-subtitle">开发工作台 - 现代化版本</span>
        </div>
        <div className="app-time">
          {currentTime.toLocaleString()}
        </div>
      </header>

      {/* 导航标签 */}
      <nav className="app-nav">
        <button
          className={`nav-tab ${activeTab === 'dashboard' ? 'active' : ''}`}
          onClick={() => setActiveTab('dashboard')}
        >
          📊 仪表板
        </button>
        <button
          className={`nav-tab ${activeTab === 'projects' ? 'active' : ''}`}
          onClick={() => setActiveTab('projects')}
        >
          📁 项目管理
        </button>
        <button
          className={`nav-tab ${activeTab === 'files' ? 'active' : ''}`}
          onClick={() => setActiveTab('files')}
        >
          📄 文件浏览
        </button>
        <button
          className={`nav-tab ${activeTab === 'api' ? 'active' : ''}`}
          onClick={() => setActiveTab('api')}
        >
          🔌 API 测试
        </button>
      </nav>

      <main className="app-content">
        {/* 仪表板 */}
        {activeTab === 'dashboard' && (
          <div className="dashboard-section">
            <h2>🎉 DevWorkbench 正在运行！</h2>
            <p>这是一个完全功能的 React 应用，展示了现代化的开发工作台。</p>

            <div className="status-section">
              <h3>📊 系统状态</h3>
              <div className="status-grid">
                <div className="status-card">
                  <h4>🟢 服务器状态</h4>
                  <p>{systemStatus.server}</p>
                </div>
                <div className="status-card">
                  <h4>⚡ React 应用</h4>
                  <p>{systemStatus.react}</p>
                </div>
                <div className="status-card">
                  <h4>🔧 构建系统</h4>
                  <p>{systemStatus.build}</p>
                </div>
                <div className="status-card">
                  <h4>💾 内存使用</h4>
                  <p>{systemStatus.memory}</p>
                </div>
                <div className="status-card">
                  <h4>⏱️ 运行时间</h4>
                  <p>{systemStatus.uptime}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 项目管理 */}
        {activeTab === 'projects' && (
          <div className="projects-section">
            <div className="section-header">
              <h3>📁 项目管理</h3>
              <button className="refresh-btn" onClick={handleRefreshProjects} disabled={loading}>
                {loading ? '🔄 刷新中...' : '🔄 刷新'}
              </button>
            </div>

            {loading ? (
              <div className="loading">加载中...</div>
            ) : (
              <div className="projects-list">
                {projects.map((project, index) => (
                  <div key={index} className="project-card enhanced">
                    <div className="project-info">
                      <h4>{project.name}</h4>
                      <p className="project-path">📂 {project.path}</p>
                      {project.lastModified && (
                        <p className="project-modified">🕒 {project.lastModified}</p>
                      )}
                      {project.gitStatus && (
                        <p className="project-git">🔀 Git: {project.gitStatus}</p>
                      )}
                    </div>
                    <div className="project-actions">
                      <span className={`status ${project.status === '运行中' ? 'running' : 'ready'}`}>
                        {project.status}
                      </span>
                      <button
                        className="action-btn"
                        onClick={() => handleProjectAction(project.name, 'scan')}
                      >
                        🔍 扫描
                      </button>
                      <button
                        className="action-btn"
                        onClick={() => handleProjectAction(project.name, 'git-status')}
                      >
                        📊 Git状态
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* 文件浏览 */}
        {activeTab === 'files' && (
          <div className="files-section">
            <h3>📄 文件浏览</h3>
            <div className="file-browser">
              <div className="file-tree">
                <div className="file-item folder">
                  <span>📁 src/</span>
                  <div className="file-children">
                    <div className="file-item">📄 renderer/</div>
                    <div className="file-item">📄 main/</div>
                  </div>
                </div>
                <div className="file-item folder">
                  <span>📁 scripts/</span>
                  <div className="file-children">
                    <div className="file-item">📄 web-enhanced.ts</div>
                    <div className="file-item">📄 cli.ts</div>
                  </div>
                </div>
                <div className="file-item">📄 package.json</div>
                <div className="file-item">📄 README.md</div>
              </div>
            </div>
          </div>
        )}

        {/* API 测试 */}
        {activeTab === 'api' && (
          <div className="api-section">
            <h3>🔌 API 测试</h3>
            <div className="api-controls">
              <button className="api-btn" onClick={handleApiTest}>
                🧪 测试 /api/status
              </button>
              <button className="api-btn" onClick={() => handleApiTest()}>
                📊 获取项目列表
              </button>
            </div>

            {apiResult && (
              <div className="api-result">
                <h4>📋 API 响应:</h4>
                <pre>{apiResult}</pre>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  );
};

export default App;
