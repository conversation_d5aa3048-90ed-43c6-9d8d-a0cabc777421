import { injectable, inject } from "inversify";
import { spawn, ChildProcess } from "child_process";
import { IProcessLauncher, ProcessConfig, ProcessHandle, ProcessStatus, ILogger, TYPES, IEventBus, CoreEvents } from "../../shared/types";

@injectable()
export class ProcessLauncher implements IProcessLauncher {
  private processes: Map<string, { handle: ProcessHandle; process: ChildProcess }> = new Map();

  constructor(
    @inject(TYPES.Logger) private logger: ILogger,
    @inject(TYPES.EventBus) private eventBus: IEventBus
  ) {}

  async start(config: ProcessConfig): Promise<ProcessHandle> {
    this.logger.info(`Starting process: ${config.name}`, { command: config.command });

    const childProcess = spawn(config.command, config.args || [], {
      cwd: config.cwd,
      env: { ...process.env, ...config.env },
      shell: config.shell ?? true,
      stdio: "pipe",
    });

    const handle: ProcessHandle = {
      id: config.id,
      pid: childProcess.pid!,
      config,
      status: ProcessStatus.RUNNING,
      outputStream: childProcess.stdout!,
      errorStream: childProcess.stderr!,
    };

    this.processes.set(handle.id, { handle, process: childProcess });
    this.eventBus.emitEvent(CoreEvents.PROCESS_STARTED, { processId: handle.id, config });

    childProcess.stdout.on("data", (data: Buffer) => {
      this.eventBus.emitEvent(CoreEvents.PROCESS_OUTPUT, { processId: handle.id, data: data.toString(), type: "stdout" });
    });

    childProcess.stderr.on("data", (data: Buffer) => {
      this.eventBus.emitEvent(CoreEvents.PROCESS_OUTPUT, { processId: handle.id, data: data.toString(), type: "stderr" });
    });

    childProcess.on("exit", (code) => {
      handle.status = code === 0 ? ProcessStatus.STOPPED : ProcessStatus.CRASHED;
      this.processes.delete(handle.id);
      this.eventBus.emitEvent(CoreEvents.PROCESS_STOPPED, { processId: handle.id, exitCode: code });
      this.logger.info(`Process ${handle.config.name} exited with code ${code}.`);
    });

    childProcess.on("error", (err) => {
      handle.status = ProcessStatus.CRASHED;
      this.processes.delete(handle.id);
      this.eventBus.emitEvent(CoreEvents.PROCESS_ERROR, { processId: handle.id, error: err.message });
      this.logger.error(`Process ${handle.config.name} encountered an error.`, err);
    });

    return handle;
  }

  async stop(handle: ProcessHandle): Promise<void> {
    const procInfo = this.processes.get(handle.id);
    if (procInfo) {
      this.logger.info(`Stopping process: ${handle.config.name} (PID: ${handle.pid})`);
      procInfo.process.kill("SIGTERM");
      // Add a timeout for SIGKILL if needed
    }
  }

  async stopAll(): Promise<void> {
    this.logger.info("Stopping all managed processes.");
    for (const { handle } of this.processes.values()) {
      await this.stop(handle);
    }
  }

  getRunningProcesses(): ProcessHandle[] {
    return Array.from(this.processes.values()).map(p => p.handle);
  }
}
